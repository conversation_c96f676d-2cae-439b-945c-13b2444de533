# 孟子智能助手 - 问题解决指南

## 当前问题分析

您遇到的错误 "系统找不到指定的路径" 通常是由以下原因造成的：

### 1. 缺少必要的依赖包

程序需要以下Python包才能正常运行：
- opencv-python (图像处理)
- numpy (数值计算)
- Pillow (图像处理)
- keyboard (全局热键)
- pya<PERSON>gui (自动化)
- psutil (系统监控)
- pynput (输入监听)

### 2. Python环境问题

- Python版本需要3.8或更高
- tkinter需要正确安装（通常随Python一起安装）

## 解决方案

### 方案1: 安装依赖包

打开命令提示符（cmd）或PowerShell，运行以下命令：

```bash
pip install opencv-python numpy Pillow keyboard pyautogui psutil pynput
```

如果上述命令失败，尝试：

```bash
python -m pip install opencv-python numpy Pillow keyboard pyautogui psutil pynput
```

### 方案2: 使用简化版本

我已经创建了一个简化版本 `XXD_simple.py`，它只使用Python标准库：

```bash
python XXD_simple.py
```

这个版本可以帮助您：
- 测试Python环境是否正常
- 验证tkinter是否工作
- 测试基本的配置管理功能

### 方案3: 逐步诊断

运行以下测试脚本来诊断问题：

1. **基本测试**：
   ```bash
   python minimal_test.py
   ```

2. **依赖安装**：
   ```bash
   python install_dependencies.py
   ```

3. **完整测试**：
   ```bash
   python test_simple.py
   ```

## 常见问题及解决方法

### 问题1: pip命令不存在
**解决方法**：
- 确保Python正确安装
- 使用 `python -m pip` 代替 `pip`
- 重新安装Python并勾选"Add to PATH"

### 问题2: 网络连接问题
**解决方法**：
- 使用国内镜像源：
  ```bash
  pip install -i https://pypi.tuna.tsinghua.edu.cn/simple opencv-python numpy Pillow keyboard pyautogui psutil pynput
  ```

### 问题3: 权限问题
**解决方法**：
- 以管理员身份运行命令提示符
- 或使用用户安装：
  ```bash
  pip install --user opencv-python numpy Pillow keyboard pyautogui psutil pynput
  ```

### 问题4: 版本冲突
**解决方法**：
- 创建虚拟环境：
  ```bash
  python -m venv venv
  venv\Scripts\activate
  pip install -r requirements.txt
  ```

## 测试步骤

### 步骤1: 验证Python环境
```bash
python --version
python -c "import tkinter; print('tkinter OK')"
```

### 步骤2: 测试简化版本
```bash
python XXD_simple.py
```
如果简化版本能正常运行，说明基本环境没问题。

### 步骤3: 安装依赖包
```bash
pip install opencv-python numpy Pillow keyboard pyautogui psutil pynput
```

### 步骤4: 测试完整版本
```bash
python XXD.py
```

## 替代方案

如果仍然无法运行完整版本，您可以：

### 1. 使用简化版本
`XXD_simple.py` 提供了基本的界面和配置管理功能，虽然没有图像识别，但可以用来：
- 测试程序逻辑
- 管理配置文件
- 验证界面功能

### 2. 手动安装依赖
逐个安装每个依赖包：
```bash
pip install opencv-python
pip install numpy
pip install Pillow
pip install keyboard
pip install pyautogui
pip install psutil
pip install pynput
```

### 3. 使用conda（如果已安装）
```bash
conda install opencv numpy pillow
pip install keyboard pyautogui psutil pynput
```

## 获取帮助

### 检查日志
如果程序启动后立即退出，检查 `logs/mengzi.log` 文件中的错误信息。

### 环境信息
运行以下命令获取环境信息：
```bash
python -c "import sys; print('Python:', sys.version); print('Path:', sys.path)"
```

### 常用调试命令
```bash
# 检查已安装的包
pip list

# 检查特定包
pip show opencv-python

# 重新安装包
pip uninstall opencv-python
pip install opencv-python
```

## 联系支持

如果以上方法都无法解决问题，请提供以下信息：

1. Python版本：`python --version`
2. 操作系统版本
3. 错误信息的完整输出
4. 已尝试的解决方法

## 总结

大多数情况下，问题是由于缺少依赖包造成的。按照以下顺序尝试：

1. 运行 `python XXD_simple.py` 测试基本环境
2. 安装依赖包：`pip install opencv-python numpy Pillow keyboard pyautogui psutil pynput`
3. 运行完整版本：`python XXD.py`

如果简化版本能运行但完整版本不能，说明是依赖包的问题。
如果简化版本也不能运行，说明是Python环境的问题。
