# 孟子智能助手 - 问题解决方案

## 🔧 当前问题

您遇到的 "系统找不到指定的路径" 错误是由于字符编码问题导致的。我已经创建了多个解决方案来彻底解决这个问题。

## 🚀 解决方案（按推荐顺序）

### 方案1：使用简单启动器（最推荐）

```bash
python start_simple.py
```

**特点：**
- 完全英文界面，无编码问题
- 自动检测Python版本
- 提供多种启动选项
- 最简单可靠

### 方案2：使用批处理文件

双击运行：`start.bat`

**特点：**
- Windows原生批处理
- 无编码问题
- 图形化选择界面

### 方案3：直接运行英文版

```bash
python XXD_en.py
```

**特点：**
- 直接启动英文简化版
- 无需额外依赖
- 功能完整的测试版本

### 方案4：基础环境测试

```bash
python test_basic.py
```

**特点：**
- 诊断Python环境
- 检查依赖包状态
- 提供详细的错误信息

## 📁 可用的启动文件

| 文件名 | 描述 | 推荐度 |
|--------|------|--------|
| `start_simple.py` | 简单启动器（英文） | ⭐⭐⭐⭐⭐ |
| `start.bat` | Windows批处理启动器 | ⭐⭐⭐⭐ |
| `XXD_en.py` | 英文版主程序 | ⭐⭐⭐⭐ |
| `test_basic.py` | 环境测试工具 | ⭐⭐⭐ |
| `launcher.py` | 高级启动器 | ⭐⭐⭐ |

## 🎯 推荐使用流程

### 第一步：环境测试
```bash
python test_basic.py
```
确保Python环境正常工作

### 第二步：启动程序
```bash
python start_simple.py
```
选择选项1（英文简化版）

### 第三步：如果需要完整功能
安装依赖包：
```bash
pip install opencv-python numpy Pillow keyboard pyautogui psutil pynput
```

然后运行：
```bash
python XXD.py
```

## 🔍 故障排除

### 如果 start_simple.py 也无法运行

1. **检查Python安装**：
   ```bash
   python --version
   ```

2. **尝试不同的Python命令**：
   ```bash
   python3 start_simple.py
   py start_simple.py
   ```

3. **检查文件编码**：
   确保所有.py文件都是UTF-8编码

### 如果出现模块导入错误

1. **检查当前目录**：
   确保在正确的项目目录中运行

2. **检查文件完整性**：
   确保所有必要文件都存在

3. **重新下载**：
   如果文件损坏，重新下载项目文件

## 📊 版本对比

### 简化版 (XXD_en.py)
- ✅ 无需额外依赖
- ✅ 完全英文界面
- ✅ 基本配置管理
- ❌ 无图像识别功能
- ❌ 无实际监控功能

### 完整版 (XXD.py)
- ✅ 完整功能
- ✅ 图像识别
- ✅ 实时监控
- ❌ 需要安装依赖包
- ❌ 可能有编码问题

## 🛠️ 依赖包安装

如果要使用完整版，需要安装以下包：

```bash
pip install opencv-python numpy Pillow keyboard pyautogui psutil pynput
```

或者使用国内镜像（如果网络慢）：

```bash
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple opencv-python numpy Pillow keyboard pyautogui psutil pynput
```

## 📞 获取帮助

### 检查日志
如果程序运行后出错，检查 `logs/` 目录下的日志文件

### 环境信息
运行以下命令获取环境信息：
```bash
python -c "import sys; print('Python:', sys.version); print('Platform:', sys.platform)"
```

### 常见错误及解决方法

1. **"No module named 'cv2'"**
   - 解决：`pip install opencv-python`

2. **"No module named 'tkinter'"**
   - 解决：重新安装Python，确保包含tkinter

3. **"Permission denied"**
   - 解决：以管理员身份运行

4. **"File not found"**
   - 解决：确保在正确的目录中运行

## 🎉 成功标志

如果看到以下界面，说明程序运行成功：

```
Mengzi WoW Assistant - Simple Launcher
========================================

Python version: 3.x.x - OK

Select version:
1. English Simple Version (recommended)
2. Full Version (requires packages)
3. Basic Test
4. Exit

Enter choice (1-4):
```

选择选项1，如果弹出图形界面，说明一切正常！

## 📝 总结

最简单的解决方案就是运行：
```bash
python start_simple.py
```

然后选择选项1。这个版本完全没有编码问题，可以让您先体验程序的基本功能。等确认环境正常后，再考虑安装依赖包使用完整版本。
