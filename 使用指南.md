# 孟子智能助手使用指南

## 第一次使用

### 1. 环境准备
- 确保已安装Python 3.8或更高版本
- 双击运行 `start.bat` 或在命令行运行 `python XXD.py`
- 程序会自动检查并安装所需依赖包

### 2. 创建配置
1. 启动程序后，点击"创建配置"按钮
2. 输入配置名称，如"我的德鲁伊"、"战士配置"等
3. 程序会创建默认配置文件

### 3. 设置监控区域
#### 技能监控区域设置：
1. 在游戏中打开Hekili或其他技能建议插件
2. 确保技能建议图标显示在屏幕上
3. 在程序中点击"设置技能区域"
4. 按照提示设置监控区域坐标

#### 血条监控区域设置：
1. 确保角色血条在屏幕上可见
2. 点击"设置血条区域"
3. 设置血条的位置和大小

## 技能配置

### 添加技能绑定
1. 切换到"技能设置"选项卡
2. 点击"添加技能"按钮
3. 填写以下信息：
   - **技能ID**：自定义的技能标识，如"奥术飞弹"
   - **热键**：对应的游戏内按键，如"1"、"2"、"shift+1"等
   - **描述**：技能说明，便于管理

### 技能模板捕获
1. 在游戏中显示要识别的技能图标
2. 确保技能图标在监控区域内
3. 添加技能时，程序会自动捕获当前区域作为模板
4. 模板文件保存在 `templates/` 目录下

### 测试技能识别
1. 在技能列表中选择要测试的技能
2. 点击"测试识别"按钮
3. 程序会显示当前的识别结果和置信度

## 血量监控配置

### 添加血量应对措施
1. 切换到"血量设置"选项卡
2. 点击"添加动作"按钮
3. 设置以下参数：
   - **血量阈值**：触发动作的血量百分比，如30、20、10
   - **热键**：要按下的按键，如"h"（治疗石）、"s"（保命技能）
   - **描述**：动作说明
   - **冷却时间**：连续触发的最小间隔时间

### 优先级模式
- **血量优先**：血量危险时优先执行保命动作
- **技能优先**：优先执行技能循环，血量动作为辅
- **平衡模式**：根据情况智能切换

## 监控参数调整

### 技能监控参数
- **扫描间隔**：检查技能的频率，越小越灵敏但占用资源越多
- **匹配阈值**：图标匹配的精确度，建议0.8-0.95
- **按键延迟**：模拟按键的持续时间

### 血量监控参数
- **血量扫描间隔**：检查血量的频率
- **检测方法**：颜色填充或血条填充检测

## 实际使用流程

### 游戏前准备
1. 启动魔兽世界
2. 确保技能建议插件正常工作
3. 调整游戏界面，确保技能图标和血条清晰可见
4. 启动孟子智能助手

### 开始监控
1. 加载对应职业的配置文件
2. 检查技能绑定和血量设置
3. 点击"开始监控"或按下热键 ` (反引号)
4. 程序开始同时监控技能和血量

### 监控中的操作
- 按 ` 键可以快速开启/关闭监控
- 按 F8 切换血量监控模式
- 按 F12 紧急退出程序
- 查看"统计信息"选项卡了解运行状态

## 常见问题解决

### 技能识别问题
1. **识别率低**：
   - 调整匹配阈值
   - 重新捕获技能模板
   - 确保游戏界面清晰

2. **误识别**：
   - 提高匹配阈值
   - 缩小监控区域
   - 检查是否有界面遮挡

### 血量监控问题
1. **血量检测不准确**：
   - 重新设置血条区域
   - 尝试不同的检测方法
   - 调整血条颜色范围

2. **应急技能触发不及时**：
   - 降低血量扫描间隔
   - 检查热键绑定是否正确
   - 确认技能冷却时间设置

### 性能问题
1. **CPU占用过高**：
   - 增加扫描间隔
   - 减小监控区域
   - 关闭不必要的功能

2. **程序响应慢**：
   - 检查系统资源使用情况
   - 重启程序
   - 优化配置参数

## 高级技巧

### 多职业配置管理
1. 为每个职业/专精创建独立配置
2. 使用描述性的配置名称
3. 定期备份配置文件

### 技能循环优化
1. 根据实际游戏体验调整参数
2. 使用统计信息分析技能使用频率
3. 针对不同场景创建不同配置

### 血量管理策略
1. 设置多个血量阈值
2. 为不同情况配置不同应对措施
3. 合理设置冷却时间避免浪费

## 安全使用建议

1. **合理使用**：
   - 不要过度依赖程序
   - 保持对游戏的主动控制
   - 在重要战斗中谨慎使用

2. **风险控制**：
   - 定期检查程序运行状态
   - 熟悉紧急停止操作
   - 备份重要配置文件

3. **遵守规则**：
   - 了解游戏服务条款
   - 承担使用风险
   - 尊重其他玩家的游戏体验

## 故障排除

### 程序无法启动
1. 检查Python版本是否符合要求
2. 确认所有依赖包已正确安装
3. 查看日志文件 `logs/mengzi.log`

### 功能异常
1. 重启程序
2. 检查配置文件是否损坏
3. 恢复默认设置

### 获取帮助
1. 查看程序内置帮助文档
2. 检查日志文件中的错误信息
3. 确保按照说明正确操作

## 更新和维护

### 配置备份
- 定期备份 `configs/` 目录
- 导出统计信息作为参考
- 记录个人使用心得

### 程序更新
- 关注版本更新信息
- 备份当前配置后再更新
- 测试新功能的稳定性

---

**注意**：本程序仅供学习研究使用，使用时请遵守相关法律法规和游戏服务条款。
