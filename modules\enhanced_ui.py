"""
增强UI模块
提供高级功能的用户界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
from datetime import datetime, timedelta
import threading
import time
from typing import Dict, List, Any

class AdvancedStatsTab:
    """高级统计选项卡"""
    
    def __init__(self, parent_notebook, assistant):
        self.parent = parent_notebook
        self.assistant = assistant
        
        # 创建选项卡
        self.frame = ttk.Frame(parent_notebook)
        parent_notebook.add(self.frame, text="高级统计")
        
        self.create_widgets()
        
        # 自动更新线程
        self.update_thread = None
        self.is_updating = False
        
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_container = ttk.PanedWindow(self.frame, orient=tk.HORIZONTAL)
        main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧面板 - 控制和信息
        left_panel = ttk.Frame(main_container)
        main_container.add(left_panel, weight=1)
        
        # 右侧面板 - 图表
        right_panel = ttk.Frame(main_container)
        main_container.add(right_panel, weight=2)
        
        self.create_control_panel(left_panel)
        self.create_charts_panel(right_panel)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        # 实时统计
        stats_group = ttk.LabelFrame(parent, text="实时统计")
        stats_group.pack(fill=tk.X, padx=5, pady=5)
        
        # 统计标签
        self.stats_labels = {}
        stats_items = [
            ("会话时长", "session_duration"),
            ("技能使用次数", "skills_used"),
            ("血量动作次数", "health_actions"),
            ("平均CPU使用率", "avg_cpu"),
            ("平均内存使用率", "avg_memory"),
            ("当前血量", "current_health")
        ]
        
        for i, (label_text, key) in enumerate(stats_items):
            ttk.Label(stats_group, text=f"{label_text}:").grid(row=i, column=0, sticky=tk.W, padx=5, pady=2)
            self.stats_labels[key] = ttk.Label(stats_group, text="--")
            self.stats_labels[key].grid(row=i, column=1, sticky=tk.W, padx=5, pady=2)
        
        # 性能监控
        perf_group = ttk.LabelFrame(parent, text="性能监控")
        perf_group.pack(fill=tk.X, padx=5, pady=5)
        
        # CPU使用率进度条
        ttk.Label(perf_group, text="CPU使用率:").pack(anchor=tk.W, padx=5, pady=2)
        self.cpu_progress = ttk.Progressbar(perf_group, length=200, mode='determinate')
        self.cpu_progress.pack(fill=tk.X, padx=5, pady=2)
        
        # 内存使用率进度条
        ttk.Label(perf_group, text="内存使用率:").pack(anchor=tk.W, padx=5, pady=2)
        self.memory_progress = ttk.Progressbar(perf_group, length=200, mode='determinate')
        self.memory_progress.pack(fill=tk.X, padx=5, pady=2)
        
        # 控制按钮
        control_group = ttk.LabelFrame(parent, text="控制")
        control_group.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(control_group, text="开始实时更新", 
                  command=self.start_real_time_update).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(control_group, text="停止实时更新", 
                  command=self.stop_real_time_update).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(control_group, text="导出数据", 
                  command=self.export_data).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(control_group, text="生成报告", 
                  command=self.generate_report).pack(fill=tk.X, padx=5, pady=2)
    
    def create_charts_panel(self, parent):
        """创建图表面板"""
        # 创建notebook用于多个图表
        chart_notebook = ttk.Notebook(parent)
        chart_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 性能图表
        self.create_performance_chart(chart_notebook)
        
        # 技能使用图表
        self.create_skill_usage_chart(chart_notebook)
        
        # 血量变化图表
        self.create_health_chart(chart_notebook)
    
    def create_performance_chart(self, parent):
        """创建性能图表"""
        perf_frame = ttk.Frame(parent)
        parent.add(perf_frame, text="性能监控")
        
        # 创建matplotlib图表
        self.perf_fig, (self.cpu_ax, self.memory_ax) = plt.subplots(2, 1, figsize=(8, 6))
        self.perf_fig.suptitle('系统性能监控')
        
        # CPU图表
        self.cpu_ax.set_title('CPU使用率 (%)')
        self.cpu_ax.set_ylim(0, 100)
        self.cpu_line, = self.cpu_ax.plot([], [], 'b-', label='CPU')
        self.cpu_ax.legend()
        self.cpu_ax.grid(True)
        
        # 内存图表
        self.memory_ax.set_title('内存使用率 (%)')
        self.memory_ax.set_ylim(0, 100)
        self.memory_line, = self.memory_ax.plot([], [], 'r-', label='Memory')
        self.memory_ax.legend()
        self.memory_ax.grid(True)
        
        # 嵌入到tkinter
        self.perf_canvas = FigureCanvasTkAgg(self.perf_fig, perf_frame)
        self.perf_canvas.draw()
        self.perf_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 数据存储
        self.perf_data = {'time': [], 'cpu': [], 'memory': []}
        self.max_data_points = 100
    
    def create_skill_usage_chart(self, parent):
        """创建技能使用图表"""
        skill_frame = ttk.Frame(parent)
        parent.add(skill_frame, text="技能统计")
        
        # 创建matplotlib图表
        self.skill_fig, self.skill_ax = plt.subplots(figsize=(8, 6))
        self.skill_fig.suptitle('技能使用统计')
        
        # 嵌入到tkinter
        self.skill_canvas = FigureCanvasTkAgg(self.skill_fig, skill_frame)
        self.skill_canvas.draw()
        self.skill_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def create_health_chart(self, parent):
        """创建血量变化图表"""
        health_frame = ttk.Frame(parent)
        parent.add(health_frame, text="血量监控")
        
        # 创建matplotlib图表
        self.health_fig, self.health_ax = plt.subplots(figsize=(8, 6))
        self.health_fig.suptitle('血量变化监控')
        
        self.health_ax.set_title('血量百分比')
        self.health_ax.set_ylim(0, 100)
        self.health_line, = self.health_ax.plot([], [], 'g-', linewidth=2, label='Health')
        
        # 添加危险区域
        self.health_ax.axhline(y=30, color='orange', linestyle='--', alpha=0.7, label='Warning (30%)')
        self.health_ax.axhline(y=20, color='red', linestyle='--', alpha=0.7, label='Critical (20%)')
        
        self.health_ax.legend()
        self.health_ax.grid(True)
        
        # 嵌入到tkinter
        self.health_canvas = FigureCanvasTkAgg(self.health_fig, health_frame)
        self.health_canvas.draw()
        self.health_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 数据存储
        self.health_data = {'time': [], 'health': []}
    
    def start_real_time_update(self):
        """开始实时更新"""
        if not self.is_updating:
            self.is_updating = True
            self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
            self.update_thread.start()
    
    def stop_real_time_update(self):
        """停止实时更新"""
        self.is_updating = False
    
    def _update_loop(self):
        """更新循环"""
        while self.is_updating:
            try:
                self.update_stats()
                self.update_charts()
                time.sleep(1)  # 每秒更新一次
            except Exception as e:
                print(f"更新循环错误: {e}")
                time.sleep(2)
    
    def update_stats(self):
        """更新统计信息"""
        try:
            # 获取会话统计
            if hasattr(self.assistant, 'session_stats'):
                stats = self.assistant.session_stats.get_stats_summary()
                
                # 更新标签
                duration = stats.get('session_duration', 0)
                hours, remainder = divmod(duration, 3600)
                minutes, seconds = divmod(remainder, 60)
                duration_str = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
                
                self.stats_labels['session_duration'].config(text=duration_str)
                self.stats_labels['skills_used'].config(text=str(stats.get('total_skills_used', 0)))
                self.stats_labels['health_actions'].config(text=str(stats.get('total_health_actions', 0)))
            
            # 获取性能统计
            if hasattr(self.assistant, 'performance_monitor'):
                perf = self.assistant.performance_monitor
                avg_cpu = perf.get_average_cpu()
                avg_memory = perf.get_average_memory()
                
                self.stats_labels['avg_cpu'].config(text=f"{avg_cpu:.1f}%")
                self.stats_labels['avg_memory'].config(text=f"{avg_memory:.1f}%")
                
                # 更新进度条
                self.cpu_progress['value'] = avg_cpu
                self.memory_progress['value'] = avg_memory
            
            # 获取当前血量
            if hasattr(self.assistant, 'health_monitor'):
                current_health = self.assistant.health_monitor.get_current_health()
                self.stats_labels['current_health'].config(text=f"{current_health:.1f}%")
                
        except Exception as e:
            print(f"更新统计信息失败: {e}")
    
    def update_charts(self):
        """更新图表"""
        try:
            current_time = time.time()
            
            # 更新性能图表
            if hasattr(self.assistant, 'performance_monitor'):
                perf = self.assistant.performance_monitor
                
                # 添加新数据点
                self.perf_data['time'].append(current_time)
                self.perf_data['cpu'].append(perf.get_average_cpu())
                self.perf_data['memory'].append(perf.get_average_memory())
                
                # 限制数据点数量
                if len(self.perf_data['time']) > self.max_data_points:
                    for key in self.perf_data:
                        self.perf_data[key].pop(0)
                
                # 更新图表
                if len(self.perf_data['time']) > 1:
                    # 转换时间为相对时间（秒）
                    relative_times = [(t - self.perf_data['time'][0]) for t in self.perf_data['time']]
                    
                    self.cpu_line.set_data(relative_times, self.perf_data['cpu'])
                    self.memory_line.set_data(relative_times, self.perf_data['memory'])
                    
                    # 调整x轴范围
                    if relative_times:
                        self.cpu_ax.set_xlim(0, max(relative_times))
                        self.memory_ax.set_xlim(0, max(relative_times))
                    
                    self.perf_canvas.draw()
            
            # 更新血量图表
            if hasattr(self.assistant, 'health_monitor'):
                current_health = self.assistant.health_monitor.get_current_health()
                
                self.health_data['time'].append(current_time)
                self.health_data['health'].append(current_health)
                
                # 限制数据点数量
                if len(self.health_data['time']) > self.max_data_points:
                    self.health_data['time'].pop(0)
                    self.health_data['health'].pop(0)
                
                # 更新图表
                if len(self.health_data['time']) > 1:
                    relative_times = [(t - self.health_data['time'][0]) for t in self.health_data['time']]
                    
                    self.health_line.set_data(relative_times, self.health_data['health'])
                    
                    if relative_times:
                        self.health_ax.set_xlim(0, max(relative_times))
                    
                    self.health_canvas.draw()
                    
        except Exception as e:
            print(f"更新图表失败: {e}")
    
    def export_data(self):
        """导出数据"""
        try:
            # 这里可以实现数据导出功能
            messagebox.showinfo("导出数据", "数据导出功能开发中...")
        except Exception as e:
            messagebox.showerror("错误", f"导出数据失败: {e}")
    
    def generate_report(self):
        """生成报告"""
        try:
            # 这里可以实现报告生成功能
            messagebox.showinfo("生成报告", "报告生成功能开发中...")
        except Exception as e:
            messagebox.showerror("错误", f"生成报告失败: {e}")


class OptimizationTab:
    """优化建议选项卡"""
    
    def __init__(self, parent_notebook, assistant):
        self.parent = parent_notebook
        self.assistant = assistant
        
        # 创建选项卡
        self.frame = ttk.Frame(parent_notebook)
        parent_notebook.add(self.frame, text="智能优化")
        
        self.create_widgets()
    
    def create_widgets(self):
        """创建界面组件"""
        # 分析结果显示
        analysis_group = ttk.LabelFrame(self.frame, text="分析结果")
        analysis_group.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建文本框显示分析结果
        self.analysis_text = tk.Text(analysis_group, wrap=tk.WORD, height=15)
        analysis_scrollbar = ttk.Scrollbar(analysis_group, orient=tk.VERTICAL, command=self.analysis_text.yview)
        self.analysis_text.configure(yscrollcommand=analysis_scrollbar.set)
        
        self.analysis_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        analysis_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 控制按钮
        button_frame = ttk.Frame(self.frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(button_frame, text="开始分析", command=self.start_analysis).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="应用优化", command=self.apply_optimization).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="重置设置", command=self.reset_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空结果", command=self.clear_results).pack(side=tk.LEFT, padx=5)
    
    def start_analysis(self):
        """开始分析"""
        try:
            self.analysis_text.delete(1.0, tk.END)
            self.analysis_text.insert(tk.END, "正在分析...\n\n")
            
            # 在后台线程中执行分析
            analysis_thread = threading.Thread(target=self._perform_analysis, daemon=True)
            analysis_thread.start()
            
        except Exception as e:
            messagebox.showerror("错误", f"开始分析失败: {e}")
    
    def _perform_analysis(self):
        """执行分析"""
        try:
            # 模拟分析过程
            analysis_results = []
            
            # 分析技能使用效率
            analysis_results.append("=== 技能使用分析 ===")
            if hasattr(self.assistant, 'skill_monitor'):
                skill_stats = self.assistant.skill_monitor.get_stats()
                success_rate = skill_stats.get('success_rate', 0)
                
                if success_rate > 0.8:
                    analysis_results.append("✓ 技能识别成功率良好")
                else:
                    analysis_results.append("⚠ 技能识别成功率较低，建议调整匹配阈值")
            
            # 分析血量监控
            analysis_results.append("\n=== 血量监控分析 ===")
            if hasattr(self.assistant, 'health_monitor'):
                health_stats = self.assistant.health_monitor.get_stats()
                actions_triggered = health_stats.get('actions_triggered', 0)
                
                if actions_triggered > 10:
                    analysis_results.append("⚠ 血量应对措施触发频繁，建议调整阈值")
                else:
                    analysis_results.append("✓ 血量监控运行正常")
            
            # 分析性能
            analysis_results.append("\n=== 性能分析 ===")
            if hasattr(self.assistant, 'performance_monitor'):
                avg_cpu = self.assistant.performance_monitor.get_average_cpu()
                avg_memory = self.assistant.performance_monitor.get_average_memory()
                
                if avg_cpu > 70:
                    analysis_results.append("⚠ CPU使用率较高，建议增加扫描间隔")
                else:
                    analysis_results.append("✓ CPU使用率正常")
                
                if avg_memory > 80:
                    analysis_results.append("⚠ 内存使用率较高，建议重启程序")
                else:
                    analysis_results.append("✓ 内存使用率正常")
            
            # 生成优化建议
            analysis_results.append("\n=== 优化建议 ===")
            analysis_results.append("1. 定期清理历史数据以释放内存")
            analysis_results.append("2. 根据实际需要调整扫描频率")
            analysis_results.append("3. 优化技能模板以提高识别准确率")
            analysis_results.append("4. 根据职业特点调整血量阈值")
            
            # 更新UI
            result_text = "\n".join(analysis_results)
            self.analysis_text.delete(1.0, tk.END)
            self.analysis_text.insert(tk.END, result_text)
            
        except Exception as e:
            error_msg = f"分析过程中发生错误: {e}"
            self.analysis_text.delete(1.0, tk.END)
            self.analysis_text.insert(tk.END, error_msg)
    
    def apply_optimization(self):
        """应用优化"""
        try:
            messagebox.showinfo("应用优化", "优化应用功能开发中...")
        except Exception as e:
            messagebox.showerror("错误", f"应用优化失败: {e}")
    
    def reset_settings(self):
        """重置设置"""
        try:
            if messagebox.askyesno("确认", "确定要重置所有设置吗？"):
                messagebox.showinfo("重置设置", "设置重置功能开发中...")
        except Exception as e:
            messagebox.showerror("错误", f"重置设置失败: {e}")
    
    def clear_results(self):
        """清空结果"""
        self.analysis_text.delete(1.0, tk.END)
