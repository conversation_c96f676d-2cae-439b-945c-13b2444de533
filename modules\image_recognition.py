"""
图像识别模块
基于OpenCV实现技能图标匹配和血量检测
"""

import cv2
import numpy as np
import pyautogui
from typing import Tuple, Optional, List
import logging
import os

class ImageRecognition:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        # 禁用pyautogui的安全检查以提高性能
        pyautogui.FAILSAFE = False
        
    def capture_screen_region(self, region: Tuple[int, int, int, int]) -> np.ndarray:
        """
        捕获屏幕指定区域
        Args:
            region: (x, y, width, height)
        Returns:
            numpy数组格式的图像
        """
        try:
            x, y, width, height = region
            screenshot = pyautogui.screenshot(region=(x, y, width, height))
            # 转换为OpenCV格式
            img = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            return img
        except Exception as e:
            self.logger.error(f"屏幕截图失败: {e}")
            return None
    
    def match_template(self, source_img: np.ndarray, template_path: str, threshold: float = 0.8) -> Optional[Tuple[int, int, float]]:
        """
        模板匹配
        Args:
            source_img: 源图像
            template_path: 模板图像路径
            threshold: 匹配阈值
        Returns:
            匹配结果 (x, y, confidence) 或 None
        """
        try:
            if not os.path.exists(template_path):
                self.logger.warning(f"模板文件不存在: {template_path}")
                return None
            
            template = cv2.imread(template_path, cv2.IMREAD_COLOR)
            if template is None:
                self.logger.warning(f"无法加载模板图像: {template_path}")
                return None
            
            # 执行模板匹配
            result = cv2.matchTemplate(source_img, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= threshold:
                return (max_loc[0], max_loc[1], max_val)
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"模板匹配失败: {e}")
            return None
    
    def detect_health_percentage(self, health_region: Tuple[int, int, int, int], 
                                health_color_range: Tuple[Tuple[int, int, int], Tuple[int, int, int]] = None) -> Optional[float]:
        """
        检测血量百分比
        Args:
            health_region: 血条区域 (x, y, width, height)
            health_color_range: 血条颜色范围 ((lower_b, lower_g, lower_r), (upper_b, upper_g, upper_r))
        Returns:
            血量百分比 (0-100) 或 None
        """
        try:
            # 默认红色血条颜色范围
            if health_color_range is None:
                # BGR格式，红色血条的颜色范围
                lower_red = (0, 0, 100)    # 较暗的红色
                upper_red = (80, 80, 255)  # 较亮的红色
                health_color_range = (lower_red, upper_red)
            
            # 捕获血条区域
            health_img = self.capture_screen_region(health_region)
            if health_img is None:
                return None
            
            # 创建颜色掩码
            lower_bound, upper_bound = health_color_range
            mask = cv2.inRange(health_img, lower_bound, upper_bound)
            
            # 计算血条像素数量
            health_pixels = cv2.countNonZero(mask)
            total_pixels = health_img.shape[0] * health_img.shape[1]
            
            # 计算血量百分比
            if total_pixels > 0:
                health_percentage = (health_pixels / total_pixels) * 100
                return min(100, max(0, health_percentage))  # 限制在0-100范围内
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"血量检测失败: {e}")
            return None
    
    def detect_health_bar_fill(self, health_region: Tuple[int, int, int, int]) -> Optional[float]:
        """
        通过检测血条填充程度来计算血量百分比
        这种方法更适用于标准的血条UI
        """
        try:
            health_img = self.capture_screen_region(health_region)
            if health_img is None:
                return None
            
            # 转换为HSV颜色空间，更容易检测红色
            hsv = cv2.cvtColor(health_img, cv2.COLOR_BGR2HSV)
            
            # 红色在HSV中的范围
            lower_red1 = np.array([0, 50, 50])
            upper_red1 = np.array([10, 255, 255])
            lower_red2 = np.array([170, 50, 50])
            upper_red2 = np.array([180, 255, 255])
            
            # 创建红色掩码
            mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
            mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
            mask = mask1 + mask2
            
            # 水平投影，找到血条的实际宽度
            horizontal_projection = np.sum(mask, axis=0)
            
            # 找到血条的左右边界
            non_zero_cols = np.where(horizontal_projection > 0)[0]
            if len(non_zero_cols) == 0:
                return 0  # 没有检测到血条，可能血量为0
            
            # 计算血条填充百分比
            filled_width = len(non_zero_cols)
            total_width = health_img.shape[1]
            
            health_percentage = (filled_width / total_width) * 100
            return min(100, max(0, health_percentage))
            
        except Exception as e:
            self.logger.error(f"血条填充检测失败: {e}")
            return None
    
    def save_template(self, region: Tuple[int, int, int, int], save_path: str) -> bool:
        """
        保存屏幕区域为模板图像
        Args:
            region: 要保存的区域 (x, y, width, height)
            save_path: 保存路径
        Returns:
            是否保存成功
        """
        try:
            img = self.capture_screen_region(region)
            if img is None:
                return False
            
            # 确保保存目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # 保存图像
            success = cv2.imwrite(save_path, img)
            if success:
                self.logger.info(f"模板保存成功: {save_path}")
            else:
                self.logger.error(f"模板保存失败: {save_path}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"保存模板失败: {e}")
            return False
    
    def find_multiple_matches(self, source_img: np.ndarray, template_path: str, 
                            threshold: float = 0.8) -> List[Tuple[int, int, float]]:
        """
        查找多个匹配项
        Args:
            source_img: 源图像
            template_path: 模板图像路径
            threshold: 匹配阈值
        Returns:
            匹配结果列表 [(x, y, confidence), ...]
        """
        try:
            if not os.path.exists(template_path):
                return []
            
            template = cv2.imread(template_path, cv2.IMREAD_COLOR)
            if template is None:
                return []
            
            result = cv2.matchTemplate(source_img, template, cv2.TM_CCOEFF_NORMED)
            
            # 找到所有超过阈值的匹配
            locations = np.where(result >= threshold)
            matches = []
            
            for pt in zip(*locations[::-1]):  # 切换x和y坐标
                confidence = result[pt[1], pt[0]]
                matches.append((pt[0], pt[1], confidence))
            
            # 按置信度排序
            matches.sort(key=lambda x: x[2], reverse=True)
            
            return matches
            
        except Exception as e:
            self.logger.error(f"多重匹配失败: {e}")
            return []
    
    def enhance_image_for_matching(self, img: np.ndarray) -> np.ndarray:
        """
        增强图像以提高匹配准确性
        """
        try:
            # 转换为灰度图
            if len(img.shape) == 3:
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            else:
                gray = img
            
            # 应用高斯模糊减少噪声
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)
            
            # 增强对比度
            enhanced = cv2.convertScaleAbs(blurred, alpha=1.2, beta=10)
            
            return enhanced
            
        except Exception as e:
            self.logger.error(f"图像增强失败: {e}")
            return img
