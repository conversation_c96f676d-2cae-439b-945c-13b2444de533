#!/usr/bin/env python3
"""
Basic test script - English only, no Chinese characters
"""

print("=== <PERSON><PERSON><PERSON> Assistant - Basic Test ===")
print()

# Test 1: Python version
import sys
print(f"1. Python version: {sys.version}")
if sys.version_info >= (3, 8):
    print("   [OK] Python version is compatible")
else:
    print("   [ERROR] Python 3.8+ required")

# Test 2: Basic imports
print()
print("2. Testing basic imports:")

try:
    import os
    print("   [OK] os")
except:
    print("   [ERROR] os")

try:
    import json
    print("   [OK] json")
except:
    print("   [ERROR] json")

try:
    import threading
    print("   [OK] threading")
except:
    print("   [ERROR] threading")

try:
    import time
    print("   [OK] time")
except:
    print("   [ERROR] time")

# Test 3: tkinter
print()
print("3. Testing tkinter:")

try:
    import tkinter as tk
    from tkinter import ttk, messagebox
    print("   [OK] tkinter imports successful")
    
    # Try to create a simple window
    root = tk.Tk()
    root.title("Test Window")
    root.geometry("300x200")
    
    label = tk.Label(root, text="Test successful!")
    label.pack(pady=20)
    
    def close_window():
        root.destroy()
    
    button = tk.Button(root, text="Close", command=close_window)
    button.pack()
    
    print("   [OK] Test window created")
    print("   Please close the test window to continue...")
    
    root.mainloop()
    print("   [OK] tkinter test completed")
    
except Exception as e:
    print(f"   [ERROR] tkinter test failed: {e}")

# Test 4: File operations
print()
print("4. Testing file operations:")

try:
    # Test directory creation
    test_dir = "test_temp"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    print("   [OK] Directory creation")
    
    # Test file writing
    test_file = os.path.join(test_dir, "test.json")
    test_data = {"test": "data", "number": 123}
    with open(test_file, 'w') as f:
        json.dump(test_data, f)
    print("   [OK] File writing")
    
    # Test file reading
    with open(test_file, 'r') as f:
        loaded_data = json.load(f)
    print("   [OK] File reading")
    
    # Cleanup
    os.remove(test_file)
    os.rmdir(test_dir)
    print("   [OK] File cleanup")
    
except Exception as e:
    print(f"   [ERROR] File operations failed: {e}")

# Test 5: Check for dependencies
print()
print("5. Checking optional dependencies:")

dependencies = [
    ("opencv-python", "cv2"),
    ("numpy", "numpy"),
    ("Pillow", "PIL"),
    ("keyboard", "keyboard"),
    ("pyautogui", "pyautogui"),
    ("psutil", "psutil"),
    ("pynput", "pynput")
]

missing_deps = []
for dep_name, import_name in dependencies:
    try:
        __import__(import_name)
        print(f"   [OK] {dep_name}")
    except ImportError:
        print(f"   [MISSING] {dep_name}")
        missing_deps.append(dep_name)

# Summary
print()
print("=== Test Summary ===")
if missing_deps:
    print(f"Missing dependencies: {len(missing_deps)}")
    print("To install missing packages, run:")
    print(f"pip install {' '.join(missing_deps)}")
    print()
    print("You can still run the simple version without these packages.")
else:
    print("All dependencies are available!")
    print("You can run the full version.")

print()
print("Next steps:")
print("1. If basic tests passed: run 'python XXD_en.py' for simple version")
print("2. If dependencies installed: run 'python XXD.py' for full version")
print("3. Use 'run.bat' for easy startup")

input("\nPress Enter to exit...")
