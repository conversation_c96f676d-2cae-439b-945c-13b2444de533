@echo off
chcp 65001 >nul
echo 孟子智能助手 - 自动修复和运行
echo ================================
echo.

REM 检查Python
echo [1/5] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

python --version
echo ✅ Python环境正常

REM 检查tkinter
echo.
echo [2/5] 检查tkinter...
python -c "import tkinter; print('✅ tkinter可用')" 2>nul
if errorlevel 1 (
    echo ❌ tkinter不可用，请重新安装Python
    pause
    exit /b 1
)

REM 测试简化版本
echo.
echo [3/5] 测试简化版本...
echo 正在启动简化版本进行测试...
echo 如果弹出窗口，请关闭它继续...
timeout /t 2 >nul

start /wait python XXD_simple.py
echo ✅ 简化版本测试完成

REM 安装依赖包
echo.
echo [4/5] 安装依赖包...
echo 正在安装必要的依赖包，这可能需要几分钟...

pip install opencv-python numpy Pillow keyboard pyautogui psutil pynput
if errorlevel 1 (
    echo ❌ 依赖包安装失败，尝试使用国内镜像...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple opencv-python numpy Pillow keyboard pyautogui psutil pynput
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        echo 请手动运行: pip install opencv-python numpy Pillow keyboard pyautogui psutil pynput
        echo.
        echo 或者使用简化版本: python XXD_simple.py
        pause
        exit /b 1
    )
)

echo ✅ 依赖包安装完成

REM 运行完整版本
echo.
echo [5/5] 启动完整版本...
echo 正在启动孟子智能助手完整版...
echo.

python XXD.py
if errorlevel 1 (
    echo.
    echo ❌ 完整版本启动失败
    echo 请尝试运行简化版本: python XXD_simple.py
    echo 或查看问题解决指南.md
    pause
    exit /b 1
)

echo.
echo 程序已退出
pause
