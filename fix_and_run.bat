@echo off
echo <PERSON><PERSON><PERSON> - Auto Fix and Run
echo ========================================
echo.

REM Check Python
echo [1/5] Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found. Please install Python 3.8 or higher
    echo Download: https://www.python.org/downloads/
    pause
    exit /b 1
)

python --version
echo [OK] Python environment is ready

REM Check tkinter
echo.
echo [2/5] Checking tkinter...
python -c "import tkinter; print('[OK] tkinter available')" 2>nul
if errorlevel 1 (
    echo [ERROR] tkinter not available, please reinstall Python
    pause
    exit /b 1
)

REM Test simple version
echo.
echo [3/5] Testing simple version...
echo Starting simple version for testing...
echo If a window pops up, please close it to continue...
timeout /t 2 >nul

start /wait python XXD_simple.py
echo [OK] Simple version test completed

REM Install dependencies
echo.
echo [4/5] Installing dependencies...
echo Installing required packages, this may take a few minutes...

pip install opencv-python numpy Pillow keyboard pyautogui psutil pynput
if errorlevel 1 (
    echo [WARNING] Installation failed, trying with China mirror...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple opencv-python numpy Pillow keyboard pyautogui psutil pynput
    if errorlevel 1 (
        echo [ERROR] Dependencies installation failed
        echo Please run manually: pip install opencv-python numpy Pillow keyboard pyautogui psutil pynput
        echo.
        echo Or use simple version: python XXD_simple.py
        pause
        exit /b 1
    )
)

echo [OK] Dependencies installed successfully

REM Run full version
echo.
echo [5/5] Starting full version...
echo Starting Mengzi WoW Assistant full version...
echo.

python XXD.py
if errorlevel 1 (
    echo.
    echo [ERROR] Full version startup failed
    echo Please try simple version: python XXD_simple.py
    echo Or check the troubleshooting guide
    pause
    exit /b 1
)

echo.
echo Program exited
pause
