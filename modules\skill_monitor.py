"""
技能监控模块
监控Hekili等插件的技能建议区域，自动按下对应按键
"""

import threading
import time
import os
from typing import Dict, Any, Optional, Callable
import logging
from .image_recognition import ImageRecognition
from .hotkey_manager import HotkeyManager

class SkillMonitor:
    def __init__(self, image_recognition: ImageRecognition, hotkey_manager: HotkeyManager):
        self.image_recognition = image_recognition
        self.hotkey_manager = hotkey_manager
        self.logger = logging.getLogger(__name__)
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        self.stop_event = threading.Event()
        
        # 配置参数
        self.monitor_region = [1193, 755, 50, 50]  # 默认监控区域
        self.scan_interval = 0.33  # 扫描间隔
        self.threshold = 0.9  # 匹配阈值
        self.key_press_delay = 0.19  # 按键延迟
        self.auto_add_skills = True  # 自动添加技能
        
        # 技能绑定
        self.icon_bindings: Dict[str, Dict[str, Any]] = {}
        
        # 统计信息
        self.stats = {
            'total_scans': 0,
            'successful_matches': 0,
            'key_presses': 0,
            'last_match_time': None,
            'last_pressed_skill': None
        }
        
        # 回调函数
        self.on_skill_detected: Optional[Callable] = None
        self.on_skill_pressed: Optional[Callable] = None
        
        # 模板目录
        self.template_dir = "templates"
        if not os.path.exists(self.template_dir):
            os.makedirs(self.template_dir)
    
    def set_monitor_region(self, region: list):
        """设置监控区域"""
        self.monitor_region = region
        self.logger.info(f"监控区域设置为: {region}")
    
    def set_scan_interval(self, interval: float):
        """设置扫描间隔"""
        self.scan_interval = max(0.1, interval)
        self.logger.info(f"扫描间隔设置为: {interval}秒")
    
    def set_threshold(self, threshold: float):
        """设置匹配阈值"""
        self.threshold = max(0.1, min(1.0, threshold))
        self.logger.info(f"匹配阈值设置为: {threshold}")
    
    def set_key_press_delay(self, delay: float):
        """设置按键延迟"""
        self.key_press_delay = max(0.01, delay)
        self.hotkey_manager.set_key_press_delay(delay)
        self.logger.info(f"按键延迟设置为: {delay}秒")
    
    def add_skill_binding(self, skill_id: str, hotkey: str, template_path: str, description: str = ""):
        """
        添加技能绑定
        Args:
            skill_id: 技能ID
            hotkey: 对应的热键
            template_path: 模板图像路径
            description: 技能描述
        """
        self.icon_bindings[skill_id] = {
            'hotkey': hotkey,
            'template_path': template_path,
            'description': description,
            'match_count': 0,
            'last_match_time': None
        }
        self.logger.info(f"技能绑定添加: {skill_id} -> {hotkey}")
    
    def remove_skill_binding(self, skill_id: str):
        """移除技能绑定"""
        if skill_id in self.icon_bindings:
            del self.icon_bindings[skill_id]
            self.logger.info(f"技能绑定移除: {skill_id}")
    
    def get_skill_bindings(self) -> Dict[str, Dict[str, Any]]:
        """获取所有技能绑定"""
        return self.icon_bindings.copy()
    
    def capture_skill_template(self, skill_id: str, region: Optional[list] = None) -> bool:
        """
        捕获技能模板
        Args:
            skill_id: 技能ID
            region: 捕获区域，None使用当前监控区域
        Returns:
            是否成功
        """
        try:
            if region is None:
                region = self.monitor_region
            
            template_path = os.path.join(self.template_dir, f"{skill_id}.png")
            success = self.image_recognition.save_template(tuple(region), template_path)
            
            if success:
                self.logger.info(f"技能模板捕获成功: {skill_id}")
                return True
            else:
                self.logger.error(f"技能模板捕获失败: {skill_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"捕获技能模板失败 {skill_id}: {e}")
            return False
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            self.logger.warning("监控已在运行中")
            return
        
        if not self.icon_bindings:
            self.logger.warning("没有配置技能绑定，无法开始监控")
            return
        
        self.is_monitoring = True
        self.stop_event.clear()
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info("技能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        self.stop_event.set()
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2.0)
        
        self.logger.info("技能监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        self.logger.info("技能监控循环开始")
        
        while self.is_monitoring and not self.stop_event.is_set():
            try:
                self._scan_and_match()
                time.sleep(self.scan_interval)
                
            except Exception as e:
                self.logger.error(f"监控循环错误: {e}")
                time.sleep(1.0)  # 错误时等待更长时间
        
        self.logger.info("技能监控循环结束")
    
    def _scan_and_match(self):
        """扫描并匹配技能"""
        try:
            # 捕获监控区域
            screen_img = self.image_recognition.capture_screen_region(tuple(self.monitor_region))
            if screen_img is None:
                return
            
            self.stats['total_scans'] += 1
            
            # 遍历所有技能绑定进行匹配
            best_match = None
            best_confidence = 0
            
            for skill_id, binding in self.icon_bindings.items():
                template_path = binding['template_path']
                
                if not os.path.exists(template_path):
                    continue
                
                # 执行模板匹配
                match_result = self.image_recognition.match_template(
                    screen_img, template_path, self.threshold
                )
                
                if match_result:
                    x, y, confidence = match_result
                    
                    # 找到最佳匹配
                    if confidence > best_confidence:
                        best_match = {
                            'skill_id': skill_id,
                            'binding': binding,
                            'confidence': confidence,
                            'position': (x, y)
                        }
                        best_confidence = confidence
            
            # 执行最佳匹配的技能
            if best_match:
                self._execute_skill(best_match)
                
        except Exception as e:
            self.logger.error(f"扫描匹配失败: {e}")
    
    def _execute_skill(self, match_info: Dict[str, Any]):
        """执行技能"""
        try:
            skill_id = match_info['skill_id']
            binding = match_info['binding']
            confidence = match_info['confidence']
            
            # 更新统计信息
            self.stats['successful_matches'] += 1
            self.stats['last_match_time'] = time.time()
            self.stats['last_pressed_skill'] = skill_id
            
            # 更新技能绑定统计
            binding['match_count'] += 1
            binding['last_match_time'] = time.time()
            
            # 触发回调
            if self.on_skill_detected:
                self.on_skill_detected(skill_id, confidence)
            
            # 按下对应按键
            hotkey = binding['hotkey']
            self.hotkey_manager.press_key_async(hotkey, self.key_press_delay)
            
            self.stats['key_presses'] += 1
            
            # 触发按键回调
            if self.on_skill_pressed:
                self.on_skill_pressed(skill_id, hotkey, confidence)
            
            self.logger.debug(f"技能执行: {skill_id} -> {hotkey} (置信度: {confidence:.3f})")
            
        except Exception as e:
            self.logger.error(f"技能执行失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        # 计算成功率
        if stats['total_scans'] > 0:
            stats['success_rate'] = stats['successful_matches'] / stats['total_scans']
        else:
            stats['success_rate'] = 0
        
        # 添加技能绑定统计
        stats['skill_stats'] = {}
        for skill_id, binding in self.icon_bindings.items():
            stats['skill_stats'][skill_id] = {
                'match_count': binding.get('match_count', 0),
                'last_match_time': binding.get('last_match_time'),
                'description': binding.get('description', '')
            }
        
        return stats
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_scans': 0,
            'successful_matches': 0,
            'key_presses': 0,
            'last_match_time': None,
            'last_pressed_skill': None
        }
        
        # 重置技能绑定统计
        for binding in self.icon_bindings.values():
            binding['match_count'] = 0
            binding['last_match_time'] = None
        
        self.logger.info("统计信息已重置")
    
    def auto_add_skill_from_region(self, skill_id: str, hotkey: str, description: str = "") -> bool:
        """
        从当前监控区域自动添加技能
        Args:
            skill_id: 技能ID
            hotkey: 对应热键
            description: 技能描述
        Returns:
            是否成功
        """
        try:
            # 捕获模板
            template_path = os.path.join(self.template_dir, f"{skill_id}.png")
            success = self.image_recognition.save_template(tuple(self.monitor_region), template_path)
            
            if success:
                # 添加技能绑定
                self.add_skill_binding(skill_id, hotkey, template_path, description)
                self.logger.info(f"自动添加技能成功: {skill_id}")
                return True
            else:
                self.logger.error(f"自动添加技能失败: {skill_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"自动添加技能异常 {skill_id}: {e}")
            return False
    
    def test_skill_recognition(self, skill_id: str) -> Optional[float]:
        """
        测试技能识别
        Args:
            skill_id: 技能ID
        Returns:
            匹配置信度，None表示未匹配
        """
        try:
            if skill_id not in self.icon_bindings:
                self.logger.error(f"技能绑定不存在: {skill_id}")
                return None
            
            binding = self.icon_bindings[skill_id]
            template_path = binding['template_path']
            
            if not os.path.exists(template_path):
                self.logger.error(f"模板文件不存在: {template_path}")
                return None
            
            # 捕获当前屏幕
            screen_img = self.image_recognition.capture_screen_region(tuple(self.monitor_region))
            if screen_img is None:
                return None
            
            # 执行匹配
            match_result = self.image_recognition.match_template(
                screen_img, template_path, self.threshold
            )
            
            if match_result:
                x, y, confidence = match_result
                self.logger.info(f"技能识别测试成功: {skill_id}, 置信度: {confidence:.3f}")
                return confidence
            else:
                self.logger.info(f"技能识别测试失败: {skill_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"技能识别测试异常 {skill_id}: {e}")
            return None
