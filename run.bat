@echo off
echo <PERSON><PERSON><PERSON> Assistant - Quick Start
echo ===================================
echo.

echo Checking Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found
    echo Please install Python 3.8+ from https://www.python.org/
    pause
    exit /b 1
)

echo Python OK
echo.

echo Choose version:
echo 1 = Simple (no dependencies needed)
echo 2 = Full (requires packages)
echo.
set /p choice="Enter 1 or 2: "

if "%choice%"=="1" (
    echo Starting simple version...
    python XXD_simple.py
) else (
    echo Starting full version...
    python XXD.py
    if errorlevel 1 (
        echo.
        echo Full version failed. Trying simple version...
        python XXD_simple.py
    )
)

echo.
pause
