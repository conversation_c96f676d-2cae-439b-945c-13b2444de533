#!/usr/bin/env python3
"""
Simple launcher for <PERSON><PERSON><PERSON> Assistant
No Chinese characters, minimal dependencies
"""

import sys
import os
import subprocess

def main():
    print("<PERSON><PERSON><PERSON> WoW Assistant - Simple Launcher")
    print("=" * 40)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("ERROR: Python 3.8+ required")
        print(f"Current version: {sys.version}")
        input("Press Enter to exit...")
        return
    
    print(f"Python version: {sys.version.split()[0]} - OK")
    
    # Show options
    print("\nSelect version to run:")
    print("1. English Simple Version (recommended)")
    print("2. Full Version (requires packages)")
    print("3. Basic Test")
    print("4. Exit")
    
    try:
        choice = input("\nEnter choice (1-4): ").strip()
        
        if choice == "1":
            print("Starting English Simple Version...")
            if os.path.exists("XXD_en.py"):
                subprocess.run([sys.executable, "XXD_en.py"])
            else:
                print("ERROR: XXD_en.py not found")
        
        elif choice == "2":
            print("Starting Full Version...")
            if os.path.exists("XXD.py"):
                subprocess.run([sys.executable, "XXD.py"])
            else:
                print("ERROR: XXD.py not found")
        
        elif choice == "3":
            print("Starting Basic Test...")
            if os.path.exists("test_basic.py"):
                subprocess.run([sys.executable, "test_basic.py"])
            else:
                print("ERROR: test_basic.py not found")
        
        elif choice == "4":
            print("Exiting...")
            return
        
        else:
            print("Invalid choice")
    
    except KeyboardInterrupt:
        print("\nCancelled by user")
    except Exception as e:
        print(f"Error: {e}")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
