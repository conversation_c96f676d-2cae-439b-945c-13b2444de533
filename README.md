# 孟子 - 魔兽世界智能助手 v2.0

## 简介

孟子是一款专为魔兽世界设计的智能助手程序，通过监控游戏界面中的技能提示区域和血量状态，实现技能自动施放和血量预警功能。本程序基于计算机视觉技术，无需修改游戏文件，不会被检测为外挂，为玩家提供更流畅的游戏体验。

## 主要功能

### 核心功能
- **技能监控与自动施放**：监控Hekili等插件的技能建议区域，自动按下对应按键
- **血量监控系统**：实时监控角色血量，在危险时自动触发应对措施
- **双区域协同工作**：技能区域和血量区域同时监控，智能决策最优操作

### 辅助功能
- **多职业配置支持**：可保存多个职业/专精的配置文件，一键切换
- **自定义按键绑定**：为每个技能图标和血量阈值设置对应的键盘按键
- **可视化界面**：直观的用户界面，易于配置和使用
- **实时预览**：显示当前监控区域的实时预览
- **智能优先级**：可设置血量优先级，在危险时优先执行保命技能

## 系统要求

- Windows 10/11
- Python 3.8+
- 分辨率：建议1920x1080或更高
- CPU：建议Intel i5/AMD Ryzen 5或更高
- 内存：4GB以上

## 安装指南

1. 确保已安装Python 3.8或更高版本
2. 安装所需依赖：
   ```bash
   pip install -r requirements.txt
   ```
3. 运行主程序：
   ```bash
   python XXD.py
   ```

## 快速入门

### 基础设置
1. **创建配置**：
   - 点击"创建配置"按钮，输入配置名称（如"NIAODE01"）
   - 系统会自动创建配置文件，包含默认设置

2. **设置监控区域**：
   - 点击"设区"按钮，选择游戏中技能提示的位置
   - 使用方向键微调位置，回车确认

3. **设置血条区域**：
   - 点击"设置血条"按钮，选择游戏中血条的位置
   - 使用方向键微调位置，回车确认

4. **开始监控**：
   - 点击"开始监控"按钮，程序会同时监控技能和血量
   - 使用热键"~"可快速开启/关闭监控

### 血量监控设置
1. **设置血量阈值**：
   - 在"血量设置"选项卡中设置血量预警阈值（默认30%）
   - 可设置多个阈值点，对应不同的应对措施

2. **绑定应急技能**：
   - 为不同血量阈值绑定对应的应急技能
   - 例如：血量低于30%时自动使用治疗石，低于20%时自动使用保命技能

3. **设置优先级**：
   - 调整血量监控的优先级，决定在危险时是否中断当前技能循环
   - 高优先级：血量低时立即执行应急技能
   - 低优先级：完成当前技能后再执行应急技能

## 高级功能

### 双区域协同工作
程序现在支持技能区域和血量区域同时监控，并根据设定的规则智能决策：

1. **智能切换模式**：
   - **正常模式**：按照技能循环执行技能
   - **生存模式**：血量低于阈值时，优先执行治疗和防御技能
   - **爆发模式**：特定条件下（如BOSS血量低于20%）提高输出优先级

2. **条件触发系统**：
   - 可设置复杂的触发条件，如"当血量低于40%且无减伤效果时，使用减伤技能"
   - 支持多条件组合，如"当血量低于25%且治疗石冷却完成时，使用治疗石"

3. **自适应扫描频率**：
   - 根据CPU使用率和游戏帧率自动调整扫描频率
   - 战斗状态下提高扫描频率，非战斗状态下降低扫描频率以节省资源

## 热键说明

- **~（反引号）**：开始/停止监控（可在设置中修改）
- **F8**：切换血量监控模式（关闭/仅预警/自动应对）
- **F9**：开启/关闭自动添加技能
- **F10**：快速添加技能
- **F11**：设置技能监控区域
- **F12**：退出程序
- **Ctrl+H**：快速设置血条区域

## 配置参数说明

### 技能监控参数
- **扫描间隔**：检查技能图标的时间间隔（秒），建议0.2-0.5
- **匹配阈值**：图标匹配的精确度，建议0.8-0.9
- **按键延迟**：模拟按键的持续时间，建议0.1-0.3秒

### 血量监控参数
- **血量扫描间隔**：检查血量的时间间隔（秒），建议0.2-0.5
- **血量预警阈值**：触发预警的血量百分比，可设置多个阈值
- **应急措施延迟**：连续触发应急措施的最小间隔，防止技能重复施放

### 协同工作参数
- **优先级设置**：血量监控vs技能循环的优先级，1-10级
- **模式切换阈值**：自动切换工作模式的条件设置
- **资源使用限制**：限制程序资源占用的上限

## 常见问题

1. **程序无法识别技能图标或血条**
   - 确保监控区域设置正确
   - 调整匹配阈值
   - 检查游戏界面是否有遮挡

2. **血量监控不准确**
   - 确保选择了正确的血条区域
   - 调整血条颜色识别参数
   - 尝试在不同光照条件下重新设置

3. **程序占用资源过高**
   - 增加扫描间隔
   - 减小监控区域大小
   - 启用资源自适应功能

4. **应急技能触发不及时**
   - 降低血量扫描间隔
   - 提高血量监控优先级
   - 检查应急技能绑定是否正确

## 项目结构

```
WOW/
├── XXD.py                 # 主程序入口
├── requirements.txt       # 依赖包列表
├── README.md             # 说明文档
├── modules/              # 核心模块
│   ├── __init__.py
│   ├── config_manager.py    # 配置管理
│   ├── image_recognition.py # 图像识别
│   ├── hotkey_manager.py    # 热键管理
│   ├── skill_monitor.py     # 技能监控
│   ├── health_monitor.py    # 血量监控
│   └── ui_manager.py        # UI界面
├── configs/              # 配置文件目录
├── templates/            # 技能图标模板目录
└── logs/                # 日志文件目录
```

## 更新日志

### 版本 2.0.0 (2025-06-16)
- 添加血量监控系统
- 实现技能区域和血量区域协同工作
- 新增智能优先级和条件触发系统
- 优化资源使用，降低CPU占用
- 完全重构UI界面，提供更好的用户体验

### 版本 1.0.1 (2025-02-28)
- 优化UI界面
- 修复多个已知问题
- 提高技能识别准确率

### 版本 1.0.0 (2025-01-15)
- 首次发布

## 免责声明

本程序仅供学习和研究使用，使用本程序可能违反游戏服务条款，请自行承担风险。开发者不对因使用本程序导致的任何后果负责。

## 技术支持

如有问题或建议，请通过以下方式联系：
- 查看日志文件：`logs/mengzi.log`
- 检查配置文件：`configs/` 目录
- 确保所有依赖包正确安装

## 开发信息

- **开发语言**：Python 3.8+
- **UI框架**：tkinter
- **图像处理**：OpenCV
- **热键处理**：keyboard, pynput
- **自动化**：pyautogui
