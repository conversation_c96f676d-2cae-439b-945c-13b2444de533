"""
热键管理模块
处理全局热键监听和按键模拟
"""

import keyboard
import pyautogui
import time
import threading
from typing import Dict, Callable, Optional
import logging

class HotkeyManager:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.hotkey_callbacks: Dict[str, Callable] = {}
        self.is_monitoring = False
        self.key_press_delay = 0.1
        
        # 禁用pyautogui的安全检查
        pyautogui.FAILSAFE = False
        
        # 按键映射表，处理特殊按键
        self.key_mapping = {
            'space': ' ',
            'enter': '\n',
            'tab': '\t',
            'backspace': '\b',
            'delete': 'delete',
            'home': 'home',
            'end': 'end',
            'pageup': 'pageup',
            'pagedown': 'pagedown',
            'up': 'up',
            'down': 'down',
            'left': 'left',
            'right': 'right',
            'f1': 'f1', 'f2': 'f2', 'f3': 'f3', 'f4': 'f4',
            'f5': 'f5', 'f6': 'f6', 'f7': 'f7', 'f8': 'f8',
            'f9': 'f9', 'f10': 'f10', 'f11': 'f11', 'f12': 'f12',
            'ctrl': 'ctrl',
            'alt': 'alt',
            'shift': 'shift',
            'win': 'win'
        }
    
    def register_hotkey(self, hotkey: str, callback: Callable, description: str = ""):
        """
        注册全局热键
        Args:
            hotkey: 热键组合，如 'ctrl+shift+a' 或 '`'
            callback: 回调函数
            description: 热键描述
        """
        try:
            # 移除已存在的热键
            if hotkey in self.hotkey_callbacks:
                self.unregister_hotkey(hotkey)
            
            # 注册新热键
            keyboard.add_hotkey(hotkey, callback)
            self.hotkey_callbacks[hotkey] = callback
            
            self.logger.info(f"热键注册成功: {hotkey} - {description}")
            return True
            
        except Exception as e:
            self.logger.error(f"热键注册失败 {hotkey}: {e}")
            return False
    
    def unregister_hotkey(self, hotkey: str):
        """取消注册热键"""
        try:
            if hotkey in self.hotkey_callbacks:
                keyboard.remove_hotkey(hotkey)
                del self.hotkey_callbacks[hotkey]
                self.logger.info(f"热键取消注册: {hotkey}")
                return True
        except Exception as e:
            self.logger.error(f"取消热键注册失败 {hotkey}: {e}")
        return False
    
    def unregister_all_hotkeys(self):
        """取消所有热键注册"""
        try:
            for hotkey in list(self.hotkey_callbacks.keys()):
                self.unregister_hotkey(hotkey)
            self.logger.info("所有热键已取消注册")
        except Exception as e:
            self.logger.error(f"取消所有热键注册失败: {e}")
    
    def press_key(self, key: str, duration: Optional[float] = None):
        """
        模拟按键
        Args:
            key: 按键，支持单个字符、特殊按键名称或组合键
            duration: 按键持续时间，None使用默认值
        """
        try:
            if duration is None:
                duration = self.key_press_delay
            
            # 处理组合键
            if '+' in key:
                keys = key.split('+')
                # 按下所有修饰键
                for k in keys[:-1]:
                    pyautogui.keyDown(self._normalize_key(k))
                
                # 按下主键
                main_key = self._normalize_key(keys[-1])
                pyautogui.keyDown(main_key)
                time.sleep(duration)
                pyautogui.keyUp(main_key)
                
                # 释放所有修饰键
                for k in reversed(keys[:-1]):
                    pyautogui.keyUp(self._normalize_key(k))
            else:
                # 单个按键
                normalized_key = self._normalize_key(key)
                pyautogui.keyDown(normalized_key)
                time.sleep(duration)
                pyautogui.keyUp(normalized_key)
            
            self.logger.debug(f"按键模拟: {key}")
            
        except Exception as e:
            self.logger.error(f"按键模拟失败 {key}: {e}")
    
    def _normalize_key(self, key: str) -> str:
        """标准化按键名称"""
        key = key.lower().strip()
        return self.key_mapping.get(key, key)
    
    def press_key_async(self, key: str, duration: Optional[float] = None):
        """异步按键，不阻塞主线程"""
        def press():
            self.press_key(key, duration)
        
        thread = threading.Thread(target=press, daemon=True)
        thread.start()
    
    def set_key_press_delay(self, delay: float):
        """设置按键延迟"""
        self.key_press_delay = max(0.01, delay)  # 最小延迟0.01秒
        self.logger.info(f"按键延迟设置为: {delay}秒")
    
    def is_key_pressed(self, key: str) -> bool:
        """检查按键是否被按下"""
        try:
            return keyboard.is_pressed(key)
        except Exception as e:
            self.logger.error(f"检查按键状态失败 {key}: {e}")
            return False
    
    def wait_for_key(self, key: str, timeout: Optional[float] = None) -> bool:
        """
        等待按键被按下
        Args:
            key: 要等待的按键
            timeout: 超时时间（秒），None表示无限等待
        Returns:
            是否在超时前检测到按键
        """
        try:
            if timeout is None:
                keyboard.wait(key)
                return True
            else:
                start_time = time.time()
                while time.time() - start_time < timeout:
                    if keyboard.is_pressed(key):
                        return True
                    time.sleep(0.01)
                return False
        except Exception as e:
            self.logger.error(f"等待按键失败 {key}: {e}")
            return False
    
    def start_monitoring(self):
        """开始监听热键"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.logger.info("热键监听已启动")
    
    def stop_monitoring(self):
        """停止监听热键"""
        if self.is_monitoring:
            self.is_monitoring = False
            self.unregister_all_hotkeys()
            self.logger.info("热键监听已停止")
    
    def get_registered_hotkeys(self) -> Dict[str, str]:
        """获取已注册的热键列表"""
        return {hotkey: str(callback) for hotkey, callback in self.hotkey_callbacks.items()}
    
    def simulate_mouse_click(self, x: int, y: int, button: str = 'left', clicks: int = 1):
        """
        模拟鼠标点击
        Args:
            x, y: 点击坐标
            button: 鼠标按键 ('left', 'right', 'middle')
            clicks: 点击次数
        """
        try:
            pyautogui.click(x, y, clicks=clicks, button=button)
            self.logger.debug(f"鼠标点击: ({x}, {y}), 按键: {button}, 次数: {clicks}")
        except Exception as e:
            self.logger.error(f"鼠标点击失败: {e}")
    
    def get_mouse_position(self) -> tuple:
        """获取当前鼠标位置"""
        try:
            return pyautogui.position()
        except Exception as e:
            self.logger.error(f"获取鼠标位置失败: {e}")
            return (0, 0)
    
    def type_text(self, text: str, interval: float = 0.01):
        """
        输入文本
        Args:
            text: 要输入的文本
            interval: 字符间隔时间
        """
        try:
            pyautogui.typewrite(text, interval=interval)
            self.logger.debug(f"文本输入: {text}")
        except Exception as e:
            self.logger.error(f"文本输入失败: {e}")
    
    def hold_key(self, key: str):
        """按住按键"""
        try:
            normalized_key = self._normalize_key(key)
            pyautogui.keyDown(normalized_key)
            self.logger.debug(f"按住按键: {key}")
        except Exception as e:
            self.logger.error(f"按住按键失败 {key}: {e}")
    
    def release_key(self, key: str):
        """释放按键"""
        try:
            normalized_key = self._normalize_key(key)
            pyautogui.keyUp(normalized_key)
            self.logger.debug(f"释放按键: {key}")
        except Exception as e:
            self.logger.error(f"释放按键失败 {key}: {e}")
    
    def emergency_stop(self):
        """紧急停止所有操作"""
        try:
            # 释放所有可能按住的按键
            common_keys = ['ctrl', 'alt', 'shift', 'win']
            for key in common_keys:
                try:
                    pyautogui.keyUp(key)
                except:
                    pass
            
            self.stop_monitoring()
            self.logger.info("紧急停止执行完成")
            
        except Exception as e:
            self.logger.error(f"紧急停止失败: {e}")
