#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
孟子智能助手 - 增强版启动器
提供完整的功能检查、依赖安装和启动选项
"""

import sys
import os
import subprocess
import json
import time
from pathlib import Path
from datetime import datetime

class EnhancedLauncher:
    """增强版启动器"""
    
    def __init__(self):
        self.version = "2.1.0"
        self.required_python = (3, 8)
        self.dependencies = {
            'opencv-python': 'cv2',
            'numpy': 'numpy', 
            'Pillow': 'PIL',
            'keyboard': 'keyboard',
            'pyautogui': 'pyautogui',
            'psutil': 'psutil',
            'pynput': 'pynput',
            'matplotlib': 'matplotlib'
        }
        
        self.optional_dependencies = {
            'tkinter': 'tkinter'  # 通常随Python安装
        }
        
    def print_banner(self):
        """打印启动横幅"""
        banner = f"""
╔══════════════════════════════════════════════════════════════╗
║                    孟子 - 魔兽世界智能助手                    ║
║                        Enhanced v{self.version}                        ║
║                                                              ║
║  一款专为魔兽世界设计的智能助手程序                          ║
║  通过计算机视觉技术实现技能自动施放和血量监控                ║
║                                                              ║
║  新增功能:                                                   ║
║  • 智能学习和优化                                           ║
║  • 高级数据分析                                             ║
║  • 性能监控和自动调优                                       ║
║  • 实时图表和统计                                           ║
║  • 自动备份和恢复                                           ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
        print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
    
    def check_python_version(self):
        """检查Python版本"""
        print("🔍 检查Python版本...")
        current_version = sys.version_info[:2]
        
        if current_version >= self.required_python:
            print(f"✅ Python版本: {sys.version.split()[0]} (符合要求)")
            return True
        else:
            print(f"❌ Python版本过低: {sys.version.split()[0]}")
            print(f"   需要Python {self.required_python[0]}.{self.required_python[1]}或更高版本")
            return False
    
    def check_dependencies(self):
        """检查依赖包"""
        print("\n🔍 检查依赖包...")
        missing_deps = []
        optional_missing = []
        
        # 检查必需依赖
        for package, import_name in self.dependencies.items():
            try:
                __import__(import_name)
                print(f"✅ {package}")
            except ImportError:
                print(f"❌ {package} (缺失)")
                missing_deps.append(package)
        
        # 检查可选依赖
        for package, import_name in self.optional_dependencies.items():
            try:
                __import__(import_name)
                print(f"✅ {package} (可选)")
            except ImportError:
                print(f"⚠️  {package} (可选，缺失)")
                optional_missing.append(package)
        
        return missing_deps, optional_missing
    
    def install_dependencies(self, missing_deps):
        """安装缺失的依赖包"""
        if not missing_deps:
            return True
        
        print(f"\n📦 需要安装 {len(missing_deps)} 个依赖包:")
        for dep in missing_deps:
            print(f"   • {dep}")
        
        try:
            response = input("\n是否现在安装这些依赖包? (y/n): ").lower().strip()
            if response != 'y':
                print("跳过依赖安装")
                return False
        except KeyboardInterrupt:
            print("\n用户取消安装")
            return False
        
        print("\n🔧 正在安装依赖包...")
        success_count = 0
        
        for package in missing_deps:
            print(f"正在安装 {package}...")
            try:
                result = subprocess.run(
                    [sys.executable, "-m", "pip", "install", package],
                    capture_output=True,
                    text=True,
                    timeout=300  # 5分钟超时
                )
                
                if result.returncode == 0:
                    print(f"✅ {package} 安装成功")
                    success_count += 1
                else:
                    print(f"❌ {package} 安装失败:")
                    print(f"   {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print(f"❌ {package} 安装超时")
            except Exception as e:
                print(f"❌ {package} 安装异常: {e}")
        
        print(f"\n📊 安装结果: {success_count}/{len(missing_deps)} 个包安装成功")
        return success_count == len(missing_deps)
    
    def check_environment(self):
        """检查运行环境"""
        print("\n🔍 检查运行环境...")
        
        # 检查必要目录
        required_dirs = ['configs', 'templates', 'logs', 'data', 'backups']
        for dir_name in required_dirs:
            dir_path = Path(dir_name)
            if not dir_path.exists():
                dir_path.mkdir(exist_ok=True)
                print(f"✅ 创建目录: {dir_name}")
            else:
                print(f"✅ 目录存在: {dir_name}")
        
        # 检查核心模块
        print("\n🔍 检查核心模块...")
        core_modules = [
            'modules/config_manager.py',
            'modules/image_recognition.py',
            'modules/hotkey_manager.py',
            'modules/skill_monitor.py',
            'modules/health_monitor.py',
            'modules/ui_manager.py'
        ]
        
        missing_modules = []
        for module_path in core_modules:
            if Path(module_path).exists():
                print(f"✅ {module_path}")
            else:
                print(f"❌ {module_path} (缺失)")
                missing_modules.append(module_path)
        
        if missing_modules:
            print(f"\n⚠️  警告: 缺少 {len(missing_modules)} 个核心模块")
            print("程序可能无法正常运行")
            return False
        
        return True
    
    def show_launch_options(self):
        """显示启动选项"""
        print("\n🚀 启动选项:")
        print("1. 完整版 (所有功能)")
        print("2. 简化版 (基本功能)")
        print("3. 英文版 (English UI)")
        print("4. 测试模式 (仅测试)")
        print("5. 配置向导")
        print("6. 退出")
        print()
        
        while True:
            try:
                choice = input("请选择启动选项 (1-6): ").strip()
                if choice in ['1', '2', '3', '4', '5', '6']:
                    return choice
                else:
                    print("无效选择，请输入1-6")
            except KeyboardInterrupt:
                print("\n用户取消")
                return '6'
    
    def launch_application(self, choice):
        """启动应用程序"""
        print(f"\n🚀 启动应用程序...")
        
        try:
            if choice == '1':
                print("启动完整版...")
                subprocess.run([sys.executable, "XXD.py"])
                
            elif choice == '2':
                print("启动简化版...")
                subprocess.run([sys.executable, "XXD_simple.py"])
                
            elif choice == '3':
                print("启动英文版...")
                subprocess.run([sys.executable, "XXD_en.py"])
                
            elif choice == '4':
                print("启动测试模式...")
                subprocess.run([sys.executable, "test_basic.py"])
                
            elif choice == '5':
                print("启动配置向导...")
                self.run_config_wizard()
                
            elif choice == '6':
                print("退出程序")
                return
                
        except FileNotFoundError as e:
            print(f"❌ 文件未找到: {e}")
            print("请确保所有必要文件都存在")
        except Exception as e:
            print(f"❌ 启动失败: {e}")
    
    def run_config_wizard(self):
        """运行配置向导"""
        print("\n🧙 配置向导")
        print("=" * 50)
        
        try:
            # 获取配置信息
            config_name = input("请输入配置名称 (如: 我的德鲁伊): ").strip()
            if not config_name:
                config_name = f"Config_{int(time.time())}"
            
            print(f"\n创建配置: {config_name}")
            
            # 创建基本配置
            config_data = {
                "monitor_region": [1193, 755, 50, 50],
                "health_region": [500, 100, 200, 30],
                "settings": {
                    "scan_interval": 0.33,
                    "threshold": 0.9,
                    "key_press_delay": 0.19,
                    "health_scan_interval": 0.3,
                    "priority_mode": "health_first"
                },
                "icon_bindings": {},
                "health_actions": {
                    "30": {"hotkey": "h", "description": "使用治疗石"},
                    "20": {"hotkey": "s", "description": "使用保命技能"}
                }
            }
            
            # 保存配置
            config_path = Path("configs") / f"{config_name}.json"
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 配置已保存: {config_path}")
            print("\n配置向导完成！")
            print("现在可以启动程序并加载此配置")
            
        except Exception as e:
            print(f"❌ 配置向导失败: {e}")
    
    def run(self):
        """运行启动器"""
        try:
            self.print_banner()
            
            # 检查Python版本
            if not self.check_python_version():
                input("\n按回车键退出...")
                return
            
            # 检查依赖
            missing_deps, optional_missing = self.check_dependencies()
            
            # 安装缺失的依赖
            if missing_deps:
                if not self.install_dependencies(missing_deps):
                    print("\n⚠️  警告: 部分依赖未安装，程序可能无法正常运行")
                    response = input("是否继续? (y/n): ").lower().strip()
                    if response != 'y':
                        return
            
            # 检查环境
            if not self.check_environment():
                print("\n⚠️  环境检查未通过，但仍可尝试运行")
            
            # 显示启动选项
            choice = self.show_launch_options()
            
            # 启动应用
            if choice != '6':
                self.launch_application(choice)
            
        except KeyboardInterrupt:
            print("\n\n用户中断程序")
        except Exception as e:
            print(f"\n❌ 启动器运行失败: {e}")
            import traceback
            traceback.print_exc()
        finally:
            print("\n感谢使用孟子智能助手！")


def main():
    """主函数"""
    launcher = EnhancedLauncher()
    launcher.run()


if __name__ == "__main__":
    main()
