@echo off
echo Mengzi - World of Warcraft Assistant v2.0
echo ==========================================
echo.

REM Check Python installation
echo Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found. Please install Python 3.8 or higher
    echo Download: https://www.python.org/downloads/
    pause
    exit /b 1
)

python --version
echo [OK] Python environment ready
echo.

REM Provide options
echo Please select running mode:
echo 1. Simple version (basic functions only, no extra dependencies)
echo 2. Full version (all features included)
echo 3. Auto-fix and run full version
echo.
set /p choice="Please enter your choice (1-3): "

if "%choice%"=="1" (
    echo.
    echo Starting simple version...
    python XXD_simple.py
    goto end
)

if "%choice%"=="3" (
    echo.
    echo Running auto-fix...
    call fix_and_run.bat
    goto end
)

REM Default run full version
echo.
echo Checking dependencies...
pip show opencv-python >nul 2>&1
if errorlevel 1 (
    echo Missing dependencies, installing...
    pip install opencv-python numpy Pillow keyboard pyautogui psutil pynput
    if errorlevel 1 (
        echo [ERROR] Dependencies installation failed
        echo Recommend running simple version or using auto-fix
        echo.
        set /p fallback="Run simple version instead? (y/n): "
        if /i "%fallback%"=="y" (
            python XXD_simple.py
        )
        goto end
    )
)

echo Starting full version...
python XXD.py

:end
echo.
echo Program exited
pause
