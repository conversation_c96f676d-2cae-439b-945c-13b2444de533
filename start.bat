@echo off
echo <PERSON><PERSON><PERSON> Assistant - Quick Start
echo ==================================
echo.

echo Checking Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found
    echo Please install Python 3.8+ from https://www.python.org/
    pause
    exit /b 1
)

echo Python found - OK
echo.

echo Select version:
echo 1. English Simple Version (recommended)
echo 2. Full Version (requires packages)
echo 3. Basic Test
echo 4. Advanced Launcher
echo.

set /p choice="Enter choice (1-4): "

if "%choice%"=="1" (
    echo Starting English Simple Version...
    python XXD_en.py
) else if "%choice%"=="2" (
    echo Starting Full Version...
    python XXD.py
) else if "%choice%"=="3" (
    echo Starting Basic Test...
    python test_basic.py
) else if "%choice%"=="4" (
    echo Starting Advanced Launcher...
    python launcher.py
) else (
    echo Invalid choice
)

echo.
pause
