@echo off
echo 孟子 - 魔兽世界智能助手 v2.0
echo ================================
echo.
echo 正在启动程序...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

REM 检查依赖包
echo 检查依赖包...
pip show opencv-python >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

REM 启动程序
echo 启动孟子智能助手...
python XXD.py

echo.
echo 程序已退出
pause
