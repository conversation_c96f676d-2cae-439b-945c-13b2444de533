@echo off
chcp 65001 >nul
echo 孟子 - 魔兽世界智能助手 v2.0
echo ================================
echo.

REM 检查Python是否安装
echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

python --version
echo ✅ Python环境正常
echo.

REM 提供选择
echo 请选择运行模式:
echo 1. 简化版本 (仅基本功能，无需额外依赖)
echo 2. 完整版本 (包含所有功能)
echo 3. 自动修复并运行完整版本
echo.
set /p choice="请输入选择 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 启动简化版本...
    python XXD_simple.py
    goto end
)

if "%choice%"=="3" (
    echo.
    echo 运行自动修复...
    call fix_and_run.bat
    goto end
)

REM 默认运行完整版本
echo.
echo 检查依赖包...
pip show opencv-python >nul 2>&1
if errorlevel 1 (
    echo 缺少依赖包，正在安装...
    pip install opencv-python numpy Pillow keyboard pyautogui psutil pynput
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        echo 建议运行简化版本或使用自动修复功能
        echo.
        set /p fallback="是否运行简化版本? (y/n): "
        if /i "%fallback%"=="y" (
            python XXD_simple.py
        )
        goto end
    )
)

echo 启动完整版本...
python XXD.py

:end
echo.
echo 程序已退出
pause
