#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
孟子 - 魔兽世界智能助手 v2.1 (Enhanced)
主程序入口

一款专为魔兽世界设计的智能助手程序，通过监控游戏界面中的技能提示区域和血量状态，
实现技能自动施放和血量预警功能。

新增功能：
- 智能学习模式
- 性能监控和优化
- 高级统计分析
- 自动备份和恢复
- 多显示器支持
- 战斗模式检测

作者: 开发者
版本: 2.1.0
日期: 2025-06-16
"""

import sys
import os
import logging
import threading
import time
import json
import shutil
import psutil
import signal
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
try:
    from modules.config_manager import ConfigManager
    from modules.image_recognition import ImageRecognition
    from modules.hotkey_manager import HotkeyManager
    from modules.skill_monitor import SkillMonitor
    from modules.health_monitor import HealthMonitor
    from modules.ui_manager import UIManager
except ImportError as e:
    print(f"模块导入失败: {e}")
    print("请确保所有依赖包已正确安装")
    print("运行: pip install opencv-python numpy Pillow keyboard pyautogui psutil pynput")
    sys.exit(1)

class MengziAssistant:
    """孟子智能助手主类 - 增强版"""

    def __init__(self):
        # 版本信息
        self.version = "2.1.0"
        self.build_date = "2025-06-16"

        # 初始化日志系统
        self.setup_logging()
        self.logger = logging.getLogger(__name__)

        # 性能监控
        self.performance_monitor = PerformanceMonitor()
        self.start_time = time.time()

        # 初始化核心组件
        try:
            self.config_manager = ConfigManager()
            self.image_recognition = ImageRecognition()
            self.hotkey_manager = HotkeyManager()

            # 初始化监控器
            self.skill_monitor = SkillMonitor(self.image_recognition, self.hotkey_manager)
            self.health_monitor = HealthMonitor(self.image_recognition, self.hotkey_manager)

            # 初始化UI管理器
            self.ui_manager = UIManager(
                self.config_manager,
                self.skill_monitor,
                self.health_monitor,
                self.hotkey_manager
            )
        except Exception as e:
            self.logger.error(f"组件初始化失败: {e}")
            raise

        # 运行状态和线程管理
        self.is_running = False
        self.coordination_thread = None
        self.performance_thread = None
        self.backup_thread = None

        # 智能学习系统
        self.learning_system = LearningSystem()
        self.battle_detector = BattleDetector()

        # 统计和分析
        self.session_stats = SessionStats()
        self.auto_backup_enabled = True
        self.backup_interval = 300  # 5分钟自动备份

        # 设置回调函数
        self.setup_callbacks()

        # 注册全局热键
        self.register_global_hotkeys()

        # 启动后台服务
        self.start_background_services()

        self.logger.info(f"孟子智能助手 v{self.version} 初始化完成")
        self.logger.info(f"构建日期: {self.build_date}")

        # 注册信号处理器
        self.setup_signal_handlers()

    def setup_signal_handlers(self):
        """设置信号处理器"""
        try:
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
        except Exception as e:
            self.logger.warning(f"信号处理器设置失败: {e}")

    def _signal_handler(self, signum, frame):
        """信号处理函数"""
        self.logger.info(f"接收到信号 {signum}，开始优雅关闭...")
        self.cleanup()
        sys.exit(0)

    def start_background_services(self):
        """启动后台服务"""
        try:
            # 启动性能监控线程
            self.performance_thread = threading.Thread(
                target=self._performance_monitor_loop,
                daemon=True
            )
            self.performance_thread.start()

            # 启动自动备份线程
            if self.auto_backup_enabled:
                self.backup_thread = threading.Thread(
                    target=self._auto_backup_loop,
                    daemon=True
                )
                self.backup_thread.start()

            self.logger.info("后台服务启动完成")

        except Exception as e:
            self.logger.error(f"后台服务启动失败: {e}")

    def _performance_monitor_loop(self):
        """性能监控循环"""
        while True:
            try:
                self.performance_monitor.update_stats()

                # 如果性能达到临界状态，自动优化
                if self.performance_monitor.is_performance_critical():
                    self._auto_optimize_performance()

                time.sleep(5)  # 每5秒检查一次

            except Exception as e:
                self.logger.error(f"性能监控循环错误: {e}")
                time.sleep(10)

    def _auto_backup_loop(self):
        """自动备份循环"""
        while True:
            try:
                time.sleep(self.backup_interval)
                self._create_auto_backup()

            except Exception as e:
                self.logger.error(f"自动备份循环错误: {e}")
                time.sleep(60)

    def _auto_optimize_performance(self):
        """自动性能优化"""
        try:
            self.logger.warning("检测到性能问题，开始自动优化...")

            # 降低扫描频率
            if self.skill_monitor.is_monitoring:
                current_interval = self.skill_monitor.scan_interval
                new_interval = min(current_interval * 1.5, 1.0)
                self.skill_monitor.set_scan_interval(new_interval)
                self.logger.info(f"技能扫描间隔调整为: {new_interval:.2f}秒")

            if self.health_monitor.is_monitoring:
                current_interval = self.health_monitor.scan_interval
                new_interval = min(current_interval * 1.3, 0.8)
                self.health_monitor.set_scan_interval(new_interval)
                self.logger.info(f"血量扫描间隔调整为: {new_interval:.2f}秒")

        except Exception as e:
            self.logger.error(f"自动性能优化失败: {e}")

    def _create_auto_backup(self):
        """创建自动备份"""
        try:
            backup_dir = Path("backups")
            backup_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"auto_backup_{timestamp}"

            # 备份配置文件
            config_backup_dir = backup_dir / backup_name / "configs"
            config_backup_dir.mkdir(parents=True, exist_ok=True)

            configs_dir = Path("configs")
            if configs_dir.exists():
                shutil.copytree(configs_dir, config_backup_dir, dirs_exist_ok=True)

            # 备份学习数据
            if hasattr(self, 'learning_system'):
                self.learning_system.save_learning_data()
                data_file = Path("data/learning_data.json")
                if data_file.exists():
                    backup_data_dir = backup_dir / backup_name / "data"
                    backup_data_dir.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(data_file, backup_data_dir)

            # 清理旧备份（保留最近10个）
            self._cleanup_old_backups(backup_dir, keep_count=10)

            self.logger.debug(f"自动备份创建完成: {backup_name}")

        except Exception as e:
            self.logger.error(f"自动备份失败: {e}")

    def _cleanup_old_backups(self, backup_dir: Path, keep_count: int = 10):
        """清理旧备份"""
        try:
            backup_folders = [d for d in backup_dir.iterdir() if d.is_dir() and d.name.startswith("auto_backup_")]
            backup_folders.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            for old_backup in backup_folders[keep_count:]:
                shutil.rmtree(old_backup)
                self.logger.debug(f"删除旧备份: {old_backup.name}")

        except Exception as e:
            self.logger.error(f"清理旧备份失败: {e}")

    def setup_logging(self):
        """设置日志系统"""
        # 创建logs目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 配置日志格式
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # 配置日志处理器
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler(log_dir / "mengzi.log", encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        # 设置第三方库日志级别
        logging.getLogger('PIL').setLevel(logging.WARNING)
        logging.getLogger('cv2').setLevel(logging.WARNING)
    
    def setup_callbacks(self):
        """设置回调函数"""
        # UI回调
        self.ui_manager.on_start_monitoring = self.start_monitoring
        self.ui_manager.on_stop_monitoring = self.stop_monitoring
        self.ui_manager.on_config_changed = self.load_config
        
        # 技能监控回调
        self.skill_monitor.on_skill_detected = self.on_skill_detected
        self.skill_monitor.on_skill_pressed = self.on_skill_pressed
        
        # 血量监控回调
        self.health_monitor.on_health_changed = self.on_health_changed
        self.health_monitor.on_health_critical = self.on_health_critical
        self.health_monitor.on_action_triggered = self.on_action_triggered
    
    def register_global_hotkeys(self):
        """注册全局热键"""
        try:
            # 主要热键
            self.hotkey_manager.register_hotkey("`", self.toggle_monitoring, "开始/停止监控")
            self.hotkey_manager.register_hotkey("F8", self.toggle_health_mode, "切换血量监控模式")
            self.hotkey_manager.register_hotkey("F9", self.toggle_auto_add, "开启/关闭自动添加技能")
            self.hotkey_manager.register_hotkey("F10", self.quick_add_skill, "快速添加技能")
            self.hotkey_manager.register_hotkey("F11", self.quick_set_skill_region, "设置技能区域")
            self.hotkey_manager.register_hotkey("F12", self.emergency_exit, "紧急退出")
            self.hotkey_manager.register_hotkey("ctrl+h", self.quick_set_health_region, "设置血条区域")
            
            self.hotkey_manager.start_monitoring()
            self.logger.info("全局热键注册完成")
            
        except Exception as e:
            self.logger.error(f"注册全局热键失败: {e}")
    
    def load_config(self, config_name: str):
        """加载配置"""
        try:
            if self.config_manager.load_config(config_name):
                # 应用配置到各个组件
                self.apply_config()
                self.logger.info(f"配置 '{config_name}' 加载成功")
                return True
            else:
                self.logger.error(f"配置 '{config_name}' 加载失败")
                return False
        except Exception as e:
            self.logger.error(f"加载配置异常: {e}")
            return False
    
    def apply_config(self):
        """应用配置到各个组件"""
        try:
            if not self.config_manager.current_config:
                return
            
            config = self.config_manager.current_config
            settings = config.get("settings", {})
            
            # 应用技能监控配置
            self.skill_monitor.set_monitor_region(config.get("monitor_region", [1193, 755, 50, 50]))
            self.skill_monitor.set_scan_interval(settings.get("scan_interval", 0.33))
            self.skill_monitor.set_threshold(settings.get("threshold", 0.9))
            self.skill_monitor.set_key_press_delay(settings.get("key_press_delay", 0.19))
            
            # 加载技能绑定
            icon_bindings = config.get("icon_bindings", {})
            for skill_id, binding in icon_bindings.items():
                self.skill_monitor.add_skill_binding(
                    skill_id,
                    binding.get("hotkey", ""),
                    binding.get("template_path", ""),
                    binding.get("text", "")
                )
            
            # 应用血量监控配置
            self.health_monitor.set_health_region(config.get("health_region", [500, 100, 200, 30]))
            self.health_monitor.set_scan_interval(settings.get("health_scan_interval", 0.3))
            self.health_monitor.set_priority_mode(settings.get("priority_mode", "health_first"))
            
            # 加载血量动作
            health_actions = config.get("health_actions", {})
            for threshold_str, action in health_actions.items():
                threshold = int(threshold_str)
                self.health_monitor.add_health_action(
                    threshold,
                    action.get("hotkey", ""),
                    action.get("description", ""),
                    action.get("cooldown", 3.0)
                )
            
            self.logger.info("配置应用完成")
            
        except Exception as e:
            self.logger.error(f"应用配置失败: {e}")
    
    def start_monitoring(self):
        """开始监控"""
        try:
            if self.is_running:
                self.logger.warning("监控已在运行中")
                return
            
            self.is_running = True
            
            # 启动技能监控
            if self.skill_monitor.get_skill_bindings():
                self.skill_monitor.start_monitoring()
                self.logger.info("技能监控已启动")
            
            # 启动血量监控
            if self.health_monitor.get_health_actions():
                self.health_monitor.start_monitoring()
                self.logger.info("血量监控已启动")
            
            # 启动协调线程
            self.start_coordination()
            
            self.logger.info("监控系统启动完成")
            
        except Exception as e:
            self.logger.error(f"启动监控失败: {e}")
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """停止监控"""
        try:
            self.is_running = False
            
            # 停止监控器
            self.skill_monitor.stop_monitoring()
            self.health_monitor.stop_monitoring()
            
            # 停止协调线程
            self.stop_coordination()
            
            self.logger.info("监控系统已停止")
            
        except Exception as e:
            self.logger.error(f"停止监控失败: {e}")
    
    def start_coordination(self):
        """启动协调线程"""
        if not self.coordination_thread or not self.coordination_thread.is_alive():
            self.coordination_thread = threading.Thread(target=self._coordination_loop, daemon=True)
            self.coordination_thread.start()
            self.logger.info("协调线程已启动")
    
    def stop_coordination(self):
        """停止协调线程"""
        # 协调线程会在is_running变为False时自动停止
        pass
    
    def _coordination_loop(self):
        """协调循环 - 处理技能监控和血量监控的协同工作"""
        self.logger.info("协调循环开始")
        
        while self.is_running:
            try:
                # 获取当前血量优先级
                health_priority = self.health_monitor.get_priority_level()
                
                # 根据优先级模式调整监控行为
                priority_mode = self.config_manager.get_setting("priority_mode", "health_first")
                
                if priority_mode == "health_first" and health_priority >= 4:
                    # 血量危险时，暂时降低技能监控频率
                    if self.skill_monitor.is_monitoring:
                        self.skill_monitor.set_scan_interval(0.5)  # 降低扫描频率
                elif priority_mode == "balanced":
                    # 平衡模式下根据血量调整
                    if health_priority >= 3:
                        self.skill_monitor.set_scan_interval(0.4)
                    else:
                        self.skill_monitor.set_scan_interval(0.33)
                
                time.sleep(1.0)  # 协调循环间隔
                
            except Exception as e:
                self.logger.error(f"协调循环错误: {e}")
                time.sleep(2.0)
        
        self.logger.info("协调循环结束")
    
    # 回调函数实现 - 增强版
    def on_skill_detected(self, skill_id: str, confidence: float):
        """技能检测回调 - 增强版"""
        self.logger.debug(f"检测到技能: {skill_id}, 置信度: {confidence:.3f}")

        # 更新统计
        self.session_stats.add_skill_usage()

        # 学习技能使用模式
        context = {
            'health_percent': self.health_monitor.get_current_health(),
            'in_combat': self.battle_detector.in_combat,
            'confidence': confidence
        }
        self.learning_system.learn_skill_pattern(skill_id, context)

    def on_skill_pressed(self, skill_id: str, hotkey: str, confidence: float):
        """技能按下回调 - 增强版"""
        self.logger.info(f"执行技能: {skill_id} -> {hotkey}, 置信度: {confidence:.3f}")

        # 记录技能使用时间和上下文
        skill_usage = {
            'skill_id': skill_id,
            'hotkey': hotkey,
            'confidence': confidence,
            'timestamp': time.time(),
            'health_percent': self.health_monitor.get_current_health(),
            'in_combat': self.battle_detector.in_combat
        }

        # 可以在这里添加技能使用历史记录
        self._record_skill_usage(skill_usage)

    def on_health_changed(self, old_health: float, new_health: float):
        """血量变化回调 - 增强版"""
        # 更新战斗检测器
        self.battle_detector.update_health(new_health, old_health)

        # 记录显著的血量变化
        if abs(old_health - new_health) > 5.0:
            self.logger.debug(f"血量变化: {old_health:.1f}% -> {new_health:.1f}%")

            # 如果血量快速下降，可能需要调整策略
            if new_health < old_health and (old_health - new_health) > 15:
                self.logger.warning(f"血量快速下降: -{old_health - new_health:.1f}%")
                self._handle_rapid_health_loss(old_health, new_health)

    def on_health_critical(self, current_health: float):
        """血量危险回调 - 增强版"""
        self.logger.warning(f"血量危险警告: {current_health:.1f}%")

        # 触发紧急模式
        self._enter_emergency_mode(current_health)

    def on_action_triggered(self, threshold: int, hotkey: str, description: str, current_health: float):
        """血量动作触发回调 - 增强版"""
        self.logger.info(f"血量应对措施触发: {description} (血量: {current_health:.1f}%)")

        # 更新统计
        self.session_stats.add_health_action()

        # 记录应对措施的效果
        self._record_health_action(threshold, hotkey, description, current_health)

    def _record_skill_usage(self, skill_usage: Dict[str, Any]):
        """记录技能使用"""
        try:
            # 这里可以实现技能使用历史记录
            # 例如保存到文件或数据库
            pass
        except Exception as e:
            self.logger.error(f"记录技能使用失败: {e}")

    def _handle_rapid_health_loss(self, old_health: float, new_health: float):
        """处理快速血量下降"""
        try:
            # 可以在这里实现紧急应对逻辑
            # 例如立即触发保命技能
            health_loss_rate = old_health - new_health

            if health_loss_rate > 20:  # 血量下降超过20%
                self.logger.critical(f"检测到严重血量下降: {health_loss_rate:.1f}%")
                # 可以触发紧急保命措施

        except Exception as e:
            self.logger.error(f"处理快速血量下降失败: {e}")

    def _enter_emergency_mode(self, current_health: float):
        """进入紧急模式"""
        try:
            self.logger.warning("进入紧急模式")

            # 暂时提高血量监控频率
            if self.health_monitor.is_monitoring:
                original_interval = self.health_monitor.scan_interval
                emergency_interval = min(original_interval * 0.5, 0.1)
                self.health_monitor.set_scan_interval(emergency_interval)

                # 5秒后恢复正常频率
                def restore_normal_mode():
                    time.sleep(5)
                    self.health_monitor.set_scan_interval(original_interval)
                    self.logger.info("退出紧急模式")

                emergency_thread = threading.Thread(target=restore_normal_mode, daemon=True)
                emergency_thread.start()

        except Exception as e:
            self.logger.error(f"进入紧急模式失败: {e}")

    def _record_health_action(self, threshold: int, hotkey: str, description: str, current_health: float):
        """记录血量应对措施"""
        try:
            action_record = {
                'threshold': threshold,
                'hotkey': hotkey,
                'description': description,
                'health_before': current_health,
                'timestamp': time.time(),
                'in_combat': self.battle_detector.in_combat
            }

            # 这里可以实现应对措施效果分析
            # 例如记录使用后的血量变化

        except Exception as e:
            self.logger.error(f"记录血量应对措施失败: {e}")
    
    # 热键处理函数 - 增强版
    def toggle_monitoring(self):
        """切换监控状态 - 增强版"""
        try:
            if self.is_running:
                self.stop_monitoring()
                self.logger.info("通过热键停止监控")
            else:
                self.start_monitoring()
                self.logger.info("通过热键开始监控")
        except Exception as e:
            self.logger.error(f"切换监控状态失败: {e}")

    def toggle_health_mode(self):
        """切换血量监控模式 - 增强版"""
        try:
            if not hasattr(self, '_health_mode_index'):
                self._health_mode_index = 0

            modes = ["关闭", "仅预警", "自动应对", "智能模式"]
            self._health_mode_index = (self._health_mode_index + 1) % len(modes)
            current_mode = modes[self._health_mode_index]

            # 根据模式调整血量监控行为
            if current_mode == "关闭":
                if self.health_monitor.is_monitoring:
                    self.health_monitor.stop_monitoring()
            elif current_mode == "仅预警":
                # 只监控不自动应对
                self.health_monitor.set_priority_mode("warning_only")
            elif current_mode == "自动应对":
                # 自动应对但不智能调整
                self.health_monitor.set_priority_mode("health_first")
            elif current_mode == "智能模式":
                # 智能调整策略
                self.health_monitor.set_priority_mode("intelligent")

            self.logger.info(f"血量监控模式切换为: {current_mode}")

        except Exception as e:
            self.logger.error(f"切换血量监控模式失败: {e}")

    def toggle_auto_add(self):
        """切换自动添加技能 - 增强版"""
        try:
            current = getattr(self.skill_monitor, 'auto_add_skills', False)
            self.skill_monitor.auto_add_skills = not current
            status = "开启" if not current else "关闭"
            self.logger.info(f"自动添加技能: {status}")

            # 如果开启自动添加，提供使用提示
            if not current:
                self.logger.info("自动添加技能已开启，按F10可快速添加当前技能")

        except Exception as e:
            self.logger.error(f"切换自动添加技能失败: {e}")

    def quick_add_skill(self):
        """快速添加技能 - 增强版"""
        try:
            self.logger.info("快速添加技能功能触发")

            # 生成技能ID
            import uuid
            skill_id = f"SKILL_{int(time.time())}"

            # 使用默认热键（可以后续修改）
            default_hotkey = "1"

            # 捕获当前监控区域作为模板
            if self.skill_monitor.capture_skill_template(skill_id):
                self.skill_monitor.add_skill_binding(
                    skill_id,
                    default_hotkey,
                    f"templates/{skill_id}.png",
                    f"快速添加的技能 {skill_id}"
                )
                self.logger.info(f"技能 {skill_id} 快速添加成功，热键: {default_hotkey}")
            else:
                self.logger.error("快速添加技能失败：无法捕获模板")

        except Exception as e:
            self.logger.error(f"快速添加技能失败: {e}")

    def quick_set_skill_region(self):
        """快速设置技能区域 - 增强版"""
        try:
            self.logger.info("快速设置技能区域功能触发")

            # 获取当前鼠标位置作为区域中心
            mouse_pos = self.hotkey_manager.get_mouse_position()
            if mouse_pos:
                x, y = mouse_pos
                # 设置50x50的区域
                region = [x - 25, y - 25, 50, 50]
                self.skill_monitor.set_monitor_region(region)
                self.logger.info(f"技能监控区域设置为: {region}")

                # 保存到当前配置
                if self.config_manager.current_config:
                    self.config_manager.set_monitor_region(region)
                    self.config_manager.save_config()
            else:
                self.logger.error("无法获取鼠标位置")

        except Exception as e:
            self.logger.error(f"快速设置技能区域失败: {e}")

    def quick_set_health_region(self):
        """快速设置血条区域 - 增强版"""
        try:
            self.logger.info("快速设置血条区域功能触发")

            # 获取当前鼠标位置作为区域中心
            mouse_pos = self.hotkey_manager.get_mouse_position()
            if mouse_pos:
                x, y = mouse_pos
                # 设置200x30的血条区域
                region = [x - 100, y - 15, 200, 30]
                self.health_monitor.set_health_region(region)
                self.logger.info(f"血条监控区域设置为: {region}")

                # 保存到当前配置
                if self.config_manager.current_config:
                    self.config_manager.set_health_region(region)
                    self.config_manager.save_config()
            else:
                self.logger.error("无法获取鼠标位置")

        except Exception as e:
            self.logger.error(f"快速设置血条区域失败: {e}")

    def emergency_exit(self):
        """紧急退出 - 增强版"""
        try:
            self.logger.critical("紧急退出触发！")

            # 立即停止所有监控
            self.is_running = False

            # 强制停止所有线程
            if hasattr(self, 'skill_monitor') and self.skill_monitor.is_monitoring:
                self.skill_monitor.stop_monitoring()

            if hasattr(self, 'health_monitor') and self.health_monitor.is_monitoring:
                self.health_monitor.stop_monitoring()

            # 释放所有按键
            self.hotkey_manager.emergency_stop()

            # 快速保存重要数据
            self._emergency_save()

            self.logger.info("紧急退出完成")

        except Exception as e:
            print(f"紧急退出过程中发生错误: {e}")
        finally:
            sys.exit(0)

    def _emergency_save(self):
        """紧急保存"""
        try:
            # 保存当前配置
            if hasattr(self, 'config_manager') and self.config_manager.current_config_name:
                self.config_manager.save_config()

            # 保存学习数据
            if hasattr(self, 'learning_system'):
                self.learning_system.save_learning_data()

            # 保存会话统计
            if hasattr(self, 'session_stats'):
                stats = self.session_stats.get_stats_summary()
                emergency_log = Path("logs") / "emergency_stats.json"
                with open(emergency_log, 'w', encoding='utf-8') as f:
                    json.dump(stats, f, indent=2, ensure_ascii=False)

        except Exception as e:
            print(f"紧急保存失败: {e}")
    
    def cleanup(self):
        """清理资源 - 增强版"""
        try:
            self.logger.info("开始清理资源...")

            # 停止监控
            self.stop_monitoring()

            # 停止热键管理器
            if hasattr(self, 'hotkey_manager'):
                self.hotkey_manager.stop_monitoring()

            # 停止后台线程
            self._stop_background_services()

            # 保存所有数据
            self._save_all_data()

            # 生成会话报告
            self._generate_session_report()

            self.logger.info("资源清理完成")

        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")

    def _stop_background_services(self):
        """停止后台服务"""
        try:
            # 标记停止
            self.is_running = False

            # 等待线程结束
            threads_to_wait = []

            if hasattr(self, 'performance_thread') and self.performance_thread and self.performance_thread.is_alive():
                threads_to_wait.append(('性能监控', self.performance_thread))

            if hasattr(self, 'backup_thread') and self.backup_thread and self.backup_thread.is_alive():
                threads_to_wait.append(('自动备份', self.backup_thread))

            if hasattr(self, 'coordination_thread') and self.coordination_thread and self.coordination_thread.is_alive():
                threads_to_wait.append(('协调', self.coordination_thread))

            # 等待线程结束（最多等待5秒）
            for thread_name, thread in threads_to_wait:
                try:
                    thread.join(timeout=2.0)
                    if thread.is_alive():
                        self.logger.warning(f"{thread_name}线程未能正常结束")
                    else:
                        self.logger.debug(f"{thread_name}线程已结束")
                except Exception as e:
                    self.logger.error(f"等待{thread_name}线程结束失败: {e}")

        except Exception as e:
            self.logger.error(f"停止后台服务失败: {e}")

    def _save_all_data(self):
        """保存所有数据"""
        try:
            # 保存配置
            if hasattr(self, 'config_manager') and self.config_manager.current_config_name:
                self.config_manager.save_config()
                self.logger.debug("配置已保存")

            # 保存学习数据
            if hasattr(self, 'learning_system'):
                self.learning_system.save_learning_data()
                self.logger.debug("学习数据已保存")

            # 创建最终备份
            if hasattr(self, 'config_manager'):
                self._create_final_backup()

        except Exception as e:
            self.logger.error(f"保存数据失败: {e}")

    def _create_final_backup(self):
        """创建最终备份"""
        try:
            backup_dir = Path("backups")
            backup_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            final_backup_name = f"final_backup_{timestamp}"

            # 备份所有重要文件
            final_backup_dir = backup_dir / final_backup_name
            final_backup_dir.mkdir(exist_ok=True)

            # 备份配置
            configs_dir = Path("configs")
            if configs_dir.exists():
                shutil.copytree(configs_dir, final_backup_dir / "configs", dirs_exist_ok=True)

            # 备份数据
            data_dir = Path("data")
            if data_dir.exists():
                shutil.copytree(data_dir, final_backup_dir / "data", dirs_exist_ok=True)

            # 备份日志（最近的）
            logs_dir = Path("logs")
            if logs_dir.exists():
                backup_logs_dir = final_backup_dir / "logs"
                backup_logs_dir.mkdir(exist_ok=True)

                # 只备份今天的日志
                today = datetime.now().strftime("%Y%m%d")
                for log_file in logs_dir.glob("*.log"):
                    if today in log_file.name or log_file.stat().st_mtime > time.time() - 86400:
                        shutil.copy2(log_file, backup_logs_dir)

            self.logger.info(f"最终备份创建完成: {final_backup_name}")

        except Exception as e:
            self.logger.error(f"创建最终备份失败: {e}")

    def _generate_session_report(self):
        """生成会话报告"""
        try:
            if not hasattr(self, 'session_stats'):
                return

            stats = self.session_stats.get_stats_summary()
            performance_stats = {
                'avg_cpu': self.performance_monitor.get_average_cpu(),
                'avg_memory': self.performance_monitor.get_average_memory()
            } if hasattr(self, 'performance_monitor') else {}

            report = {
                'session_info': {
                    'version': self.version,
                    'start_time': datetime.fromtimestamp(self.start_time).isoformat(),
                    'end_time': datetime.now().isoformat(),
                    'duration_seconds': time.time() - self.start_time
                },
                'session_stats': stats,
                'performance_stats': performance_stats,
                'battle_stats': {
                    'in_combat': self.battle_detector.in_combat,
                    'combat_start_time': self.battle_detector.combat_start_time
                } if hasattr(self, 'battle_detector') else {}
            }

            # 保存报告
            reports_dir = Path("reports")
            reports_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = reports_dir / f"session_report_{timestamp}.json"

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            self.logger.info(f"会话报告已生成: {report_file}")

        except Exception as e:
            self.logger.error(f"生成会话报告失败: {e}")

    def run(self):
        """运行主程序 - 增强版"""
        try:
            self.logger.info(f"孟子智能助手 v{self.version} 启动")
            self.logger.info(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # 显示系统信息
            self._log_system_info()

            # 检查依赖
            self._check_dependencies()

            # 运行UI
            self.ui_manager.run()

        except KeyboardInterrupt:
            self.logger.info("用户中断程序")
        except Exception as e:
            self.logger.error(f"程序运行异常: {e}")
            self.session_stats.add_error()
            import traceback
            self.logger.error(traceback.format_exc())
        finally:
            self.cleanup()

    def _log_system_info(self):
        """记录系统信息"""
        try:
            import platform
            self.logger.info(f"操作系统: {platform.system()} {platform.release()}")
            self.logger.info(f"Python版本: {sys.version}")
            self.logger.info(f"CPU核心数: {psutil.cpu_count()}")

            memory = psutil.virtual_memory()
            self.logger.info(f"总内存: {memory.total / (1024**3):.1f}GB")
            self.logger.info(f"可用内存: {memory.available / (1024**3):.1f}GB")

        except Exception as e:
            self.logger.error(f"记录系统信息失败: {e}")

    def _check_dependencies(self):
        """检查依赖"""
        try:
            required_modules = ['cv2', 'numpy', 'PIL', 'keyboard', 'pyautogui']
            missing_modules = []

            for module in required_modules:
                try:
                    __import__(module)
                except ImportError:
                    missing_modules.append(module)

            if missing_modules:
                self.logger.warning(f"缺少依赖模块: {missing_modules}")
                self.logger.warning("某些功能可能无法正常工作")
            else:
                self.logger.info("所有依赖模块检查通过")

        except Exception as e:
            self.logger.error(f"检查依赖失败: {e}")

class PerformanceMonitor:
    """性能监控类"""

    def __init__(self):
        self.cpu_usage_history = []
        self.memory_usage_history = []
        self.fps_history = []
        self.max_history_length = 100

    def update_stats(self):
        """更新性能统计"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=0.1)
            self.cpu_usage_history.append(cpu_percent)

            # 内存使用率
            memory = psutil.virtual_memory()
            self.memory_usage_history.append(memory.percent)

            # 限制历史记录长度
            if len(self.cpu_usage_history) > self.max_history_length:
                self.cpu_usage_history.pop(0)
            if len(self.memory_usage_history) > self.max_history_length:
                self.memory_usage_history.pop(0)

        except Exception as e:
            logging.getLogger(__name__).error(f"性能监控更新失败: {e}")

    def get_average_cpu(self) -> float:
        """获取平均CPU使用率"""
        return sum(self.cpu_usage_history) / len(self.cpu_usage_history) if self.cpu_usage_history else 0

    def get_average_memory(self) -> float:
        """获取平均内存使用率"""
        return sum(self.memory_usage_history) / len(self.memory_usage_history) if self.memory_usage_history else 0

    def is_performance_critical(self) -> bool:
        """检查性能是否达到临界状态"""
        return self.get_average_cpu() > 80 or self.get_average_memory() > 85


class LearningSystem:
    """智能学习系统"""

    def __init__(self):
        self.skill_patterns = {}
        self.health_patterns = {}
        self.learning_data_file = "data/learning_data.json"
        self.load_learning_data()

    def load_learning_data(self):
        """加载学习数据"""
        try:
            if os.path.exists(self.learning_data_file):
                with open(self.learning_data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.skill_patterns = data.get('skill_patterns', {})
                    self.health_patterns = data.get('health_patterns', {})
        except Exception as e:
            logging.getLogger(__name__).error(f"加载学习数据失败: {e}")

    def save_learning_data(self):
        """保存学习数据"""
        try:
            os.makedirs(os.path.dirname(self.learning_data_file), exist_ok=True)
            data = {
                'skill_patterns': self.skill_patterns,
                'health_patterns': self.health_patterns,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.learning_data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.getLogger(__name__).error(f"保存学习数据失败: {e}")

    def learn_skill_pattern(self, skill_id: str, context: Dict[str, Any]):
        """学习技能使用模式"""
        if skill_id not in self.skill_patterns:
            self.skill_patterns[skill_id] = []

        pattern = {
            'timestamp': time.time(),
            'health_percent': context.get('health_percent', 100),
            'in_combat': context.get('in_combat', False),
            'target_health': context.get('target_health', 100)
        }

        self.skill_patterns[skill_id].append(pattern)

        # 限制历史记录
        if len(self.skill_patterns[skill_id]) > 1000:
            self.skill_patterns[skill_id] = self.skill_patterns[skill_id][-500:]


class BattleDetector:
    """战斗状态检测器"""

    def __init__(self):
        self.in_combat = False
        self.combat_start_time = None
        self.last_health_change = time.time()
        self.health_change_threshold = 5.0  # 血量变化阈值

    def update_health(self, current_health: float, previous_health: float):
        """更新血量信息"""
        health_change = abs(current_health - previous_health)

        if health_change > self.health_change_threshold:
            self.last_health_change = time.time()

            # 如果血量快速下降，可能进入战斗
            if current_health < previous_health and not self.in_combat:
                self.enter_combat()

    def enter_combat(self):
        """进入战斗状态"""
        if not self.in_combat:
            self.in_combat = True
            self.combat_start_time = time.time()
            logging.getLogger(__name__).info("检测到进入战斗状态")

    def exit_combat(self):
        """退出战斗状态"""
        if self.in_combat:
            combat_duration = time.time() - self.combat_start_time if self.combat_start_time else 0
            self.in_combat = False
            self.combat_start_time = None
            logging.getLogger(__name__).info(f"检测到退出战斗状态，持续时间: {combat_duration:.1f}秒")

    def check_combat_status(self):
        """检查战斗状态"""
        # 如果超过10秒没有血量变化，可能退出战斗
        if self.in_combat and time.time() - self.last_health_change > 10:
            self.exit_combat()


class SessionStats:
    """会话统计类"""

    def __init__(self):
        self.session_start = time.time()
        self.total_skills_used = 0
        self.total_health_actions = 0
        self.combat_time = 0
        self.uptime = 0
        self.errors_count = 0

    def add_skill_usage(self):
        """记录技能使用"""
        self.total_skills_used += 1

    def add_health_action(self):
        """记录血量动作"""
        self.total_health_actions += 1

    def add_error(self):
        """记录错误"""
        self.errors_count += 1

    def get_session_duration(self) -> float:
        """获取会话持续时间"""
        return time.time() - self.session_start

    def get_stats_summary(self) -> Dict[str, Any]:
        """获取统计摘要"""
        duration = self.get_session_duration()
        return {
            'session_duration': duration,
            'total_skills_used': self.total_skills_used,
            'total_health_actions': self.total_health_actions,
            'skills_per_minute': self.total_skills_used / (duration / 60) if duration > 0 else 0,
            'uptime_percentage': (self.uptime / duration * 100) if duration > 0 else 0,
            'errors_count': self.errors_count
        }


def main():
    """主函数"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 8):
            print("错误: 需要Python 3.8或更高版本")
            sys.exit(1)

        # 创建并运行助手
        assistant = MengziAssistant()
        assistant.run()

    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
