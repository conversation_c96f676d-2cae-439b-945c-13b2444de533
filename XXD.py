#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
孟子 - 魔兽世界智能助手 v2.0
主程序入口

一款专为魔兽世界设计的智能助手程序，通过监控游戏界面中的技能提示区域和血量状态，
实现技能自动施放和血量预警功能。

作者: 开发者
版本: 2.0.0
日期: 2025-01-15
"""

import sys
import os
import logging
import threading
import time
from pathlib import Path

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
from modules.config_manager import ConfigManager
from modules.image_recognition import ImageRecognition
from modules.hotkey_manager import HotkeyManager
from modules.skill_monitor import SkillMonitor
from modules.health_monitor import HealthMonitor
from modules.ui_manager import UIManager

class MengziAssistant:
    """孟子智能助手主类"""
    
    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.config_manager = ConfigManager()
        self.image_recognition = ImageRecognition()
        self.hotkey_manager = HotkeyManager()
        
        # 初始化监控器
        self.skill_monitor = SkillMonitor(self.image_recognition, self.hotkey_manager)
        self.health_monitor = HealthMonitor(self.image_recognition, self.hotkey_manager)
        
        # 初始化UI管理器
        self.ui_manager = UIManager(
            self.config_manager,
            self.skill_monitor,
            self.health_monitor,
            self.hotkey_manager
        )
        
        # 运行状态
        self.is_running = False
        self.coordination_thread = None
        
        # 设置回调函数
        self.setup_callbacks()
        
        # 注册全局热键
        self.register_global_hotkeys()
        
        self.logger.info("孟子智能助手初始化完成")
    
    def setup_logging(self):
        """设置日志系统"""
        # 创建logs目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 配置日志格式
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # 配置日志处理器
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler(log_dir / "mengzi.log", encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        # 设置第三方库日志级别
        logging.getLogger('PIL').setLevel(logging.WARNING)
        logging.getLogger('cv2').setLevel(logging.WARNING)
    
    def setup_callbacks(self):
        """设置回调函数"""
        # UI回调
        self.ui_manager.on_start_monitoring = self.start_monitoring
        self.ui_manager.on_stop_monitoring = self.stop_monitoring
        self.ui_manager.on_config_changed = self.load_config
        
        # 技能监控回调
        self.skill_monitor.on_skill_detected = self.on_skill_detected
        self.skill_monitor.on_skill_pressed = self.on_skill_pressed
        
        # 血量监控回调
        self.health_monitor.on_health_changed = self.on_health_changed
        self.health_monitor.on_health_critical = self.on_health_critical
        self.health_monitor.on_action_triggered = self.on_action_triggered
    
    def register_global_hotkeys(self):
        """注册全局热键"""
        try:
            # 主要热键
            self.hotkey_manager.register_hotkey("`", self.toggle_monitoring, "开始/停止监控")
            self.hotkey_manager.register_hotkey("F8", self.toggle_health_mode, "切换血量监控模式")
            self.hotkey_manager.register_hotkey("F9", self.toggle_auto_add, "开启/关闭自动添加技能")
            self.hotkey_manager.register_hotkey("F10", self.quick_add_skill, "快速添加技能")
            self.hotkey_manager.register_hotkey("F11", self.quick_set_skill_region, "设置技能区域")
            self.hotkey_manager.register_hotkey("F12", self.emergency_exit, "紧急退出")
            self.hotkey_manager.register_hotkey("ctrl+h", self.quick_set_health_region, "设置血条区域")
            
            self.hotkey_manager.start_monitoring()
            self.logger.info("全局热键注册完成")
            
        except Exception as e:
            self.logger.error(f"注册全局热键失败: {e}")
    
    def load_config(self, config_name: str):
        """加载配置"""
        try:
            if self.config_manager.load_config(config_name):
                # 应用配置到各个组件
                self.apply_config()
                self.logger.info(f"配置 '{config_name}' 加载成功")
                return True
            else:
                self.logger.error(f"配置 '{config_name}' 加载失败")
                return False
        except Exception as e:
            self.logger.error(f"加载配置异常: {e}")
            return False
    
    def apply_config(self):
        """应用配置到各个组件"""
        try:
            if not self.config_manager.current_config:
                return
            
            config = self.config_manager.current_config
            settings = config.get("settings", {})
            
            # 应用技能监控配置
            self.skill_monitor.set_monitor_region(config.get("monitor_region", [1193, 755, 50, 50]))
            self.skill_monitor.set_scan_interval(settings.get("scan_interval", 0.33))
            self.skill_monitor.set_threshold(settings.get("threshold", 0.9))
            self.skill_monitor.set_key_press_delay(settings.get("key_press_delay", 0.19))
            
            # 加载技能绑定
            icon_bindings = config.get("icon_bindings", {})
            for skill_id, binding in icon_bindings.items():
                self.skill_monitor.add_skill_binding(
                    skill_id,
                    binding.get("hotkey", ""),
                    binding.get("template_path", ""),
                    binding.get("text", "")
                )
            
            # 应用血量监控配置
            self.health_monitor.set_health_region(config.get("health_region", [500, 100, 200, 30]))
            self.health_monitor.set_scan_interval(settings.get("health_scan_interval", 0.3))
            self.health_monitor.set_priority_mode(settings.get("priority_mode", "health_first"))
            
            # 加载血量动作
            health_actions = config.get("health_actions", {})
            for threshold_str, action in health_actions.items():
                threshold = int(threshold_str)
                self.health_monitor.add_health_action(
                    threshold,
                    action.get("hotkey", ""),
                    action.get("description", ""),
                    action.get("cooldown", 3.0)
                )
            
            self.logger.info("配置应用完成")
            
        except Exception as e:
            self.logger.error(f"应用配置失败: {e}")
    
    def start_monitoring(self):
        """开始监控"""
        try:
            if self.is_running:
                self.logger.warning("监控已在运行中")
                return
            
            self.is_running = True
            
            # 启动技能监控
            if self.skill_monitor.get_skill_bindings():
                self.skill_monitor.start_monitoring()
                self.logger.info("技能监控已启动")
            
            # 启动血量监控
            if self.health_monitor.get_health_actions():
                self.health_monitor.start_monitoring()
                self.logger.info("血量监控已启动")
            
            # 启动协调线程
            self.start_coordination()
            
            self.logger.info("监控系统启动完成")
            
        except Exception as e:
            self.logger.error(f"启动监控失败: {e}")
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """停止监控"""
        try:
            self.is_running = False
            
            # 停止监控器
            self.skill_monitor.stop_monitoring()
            self.health_monitor.stop_monitoring()
            
            # 停止协调线程
            self.stop_coordination()
            
            self.logger.info("监控系统已停止")
            
        except Exception as e:
            self.logger.error(f"停止监控失败: {e}")
    
    def start_coordination(self):
        """启动协调线程"""
        if not self.coordination_thread or not self.coordination_thread.is_alive():
            self.coordination_thread = threading.Thread(target=self._coordination_loop, daemon=True)
            self.coordination_thread.start()
            self.logger.info("协调线程已启动")
    
    def stop_coordination(self):
        """停止协调线程"""
        # 协调线程会在is_running变为False时自动停止
        pass
    
    def _coordination_loop(self):
        """协调循环 - 处理技能监控和血量监控的协同工作"""
        self.logger.info("协调循环开始")
        
        while self.is_running:
            try:
                # 获取当前血量优先级
                health_priority = self.health_monitor.get_priority_level()
                
                # 根据优先级模式调整监控行为
                priority_mode = self.config_manager.get_setting("priority_mode", "health_first")
                
                if priority_mode == "health_first" and health_priority >= 4:
                    # 血量危险时，暂时降低技能监控频率
                    if self.skill_monitor.is_monitoring:
                        self.skill_monitor.set_scan_interval(0.5)  # 降低扫描频率
                elif priority_mode == "balanced":
                    # 平衡模式下根据血量调整
                    if health_priority >= 3:
                        self.skill_monitor.set_scan_interval(0.4)
                    else:
                        self.skill_monitor.set_scan_interval(0.33)
                
                time.sleep(1.0)  # 协调循环间隔
                
            except Exception as e:
                self.logger.error(f"协调循环错误: {e}")
                time.sleep(2.0)
        
        self.logger.info("协调循环结束")
    
    # 回调函数实现
    def on_skill_detected(self, skill_id: str, confidence: float):
        """技能检测回调"""
        self.logger.debug(f"检测到技能: {skill_id}, 置信度: {confidence:.3f}")
    
    def on_skill_pressed(self, skill_id: str, hotkey: str, confidence: float):
        """技能按下回调"""
        self.logger.info(f"执行技能: {skill_id} -> {hotkey}, 置信度: {confidence:.3f}")
    
    def on_health_changed(self, old_health: float, new_health: float):
        """血量变化回调"""
        if abs(old_health - new_health) > 5.0:  # 只记录显著变化
            self.logger.debug(f"血量变化: {old_health:.1f}% -> {new_health:.1f}%")
    
    def on_health_critical(self, current_health: float):
        """血量危险回调"""
        self.logger.warning(f"血量危险警告: {current_health:.1f}%")
    
    def on_action_triggered(self, threshold: int, hotkey: str, description: str, current_health: float):
        """血量动作触发回调"""
        self.logger.info(f"血量应对措施触发: {description} (血量: {current_health:.1f}%)")
    
    # 热键处理函数
    def toggle_monitoring(self):
        """切换监控状态"""
        if self.is_running:
            self.stop_monitoring()
        else:
            self.start_monitoring()
    
    def toggle_health_mode(self):
        """切换血量监控模式"""
        # 这里可以实现不同的血量监控模式切换
        self.logger.info("血量监控模式切换")
    
    def toggle_auto_add(self):
        """切换自动添加技能"""
        current = self.skill_monitor.auto_add_skills
        self.skill_monitor.auto_add_skills = not current
        self.logger.info(f"自动添加技能: {'开启' if not current else '关闭'}")
    
    def quick_add_skill(self):
        """快速添加技能"""
        self.logger.info("快速添加技能功能触发")
    
    def quick_set_skill_region(self):
        """快速设置技能区域"""
        self.logger.info("快速设置技能区域功能触发")
    
    def quick_set_health_region(self):
        """快速设置血条区域"""
        self.logger.info("快速设置血条区域功能触发")
    
    def emergency_exit(self):
        """紧急退出"""
        self.logger.info("紧急退出触发")
        self.cleanup()
        sys.exit(0)
    
    def cleanup(self):
        """清理资源"""
        try:
            self.logger.info("开始清理资源...")
            
            # 停止监控
            self.stop_monitoring()
            
            # 停止热键管理器
            self.hotkey_manager.stop_monitoring()
            
            # 保存配置
            if self.config_manager.current_config_name:
                self.config_manager.save_config()
            
            self.logger.info("资源清理完成")
            
        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")
    
    def run(self):
        """运行主程序"""
        try:
            self.logger.info("孟子智能助手启动")
            
            # 运行UI
            self.ui_manager.run()
            
        except KeyboardInterrupt:
            self.logger.info("用户中断程序")
        except Exception as e:
            self.logger.error(f"程序运行异常: {e}")
        finally:
            self.cleanup()

def main():
    """主函数"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 8):
            print("错误: 需要Python 3.8或更高版本")
            sys.exit(1)
        
        # 创建并运行助手
        assistant = MengziAssistant()
        assistant.run()
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
