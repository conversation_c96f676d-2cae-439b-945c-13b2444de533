#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON><PERSON> Assistant - Enhanced Launcher
Complete functionality check, dependency installation and launch options
"""

import sys
import os
import subprocess
import json
import time
from pathlib import Path
from datetime import datetime

class MengziLauncher:
    """Enhanced Launcher for Mengzi WoW Assistant"""
    
    def __init__(self):
        self.version = "2.1.0"
        self.required_python = (3, 8)
        self.dependencies = {
            'opencv-python': 'cv2',
            'numpy': 'numpy', 
            'Pillow': 'PIL',
            'keyboard': 'keyboard',
            'pyautogui': 'pyautogui',
            'psutil': 'psutil',
            'pynput': 'pynput'
        }
        
        self.optional_dependencies = {
            'matplotlib': 'matplotlib',
            'tkinter': 'tkinter'
        }
        
    def print_banner(self):
        """Print startup banner"""
        banner = f"""
================================================================
                Mengzi - World of Warcraft Assistant                
                        Enhanced v{self.version}                        
                                                              
  An intelligent assistant designed for World of Warcraft      
  Automatic skill casting and health monitoring via CV         
                                                              
  New Features:                                               
  • Smart Learning and Optimization                          
  • Advanced Data Analytics                                  
  • Performance Monitoring and Auto-tuning                   
  • Real-time Charts and Statistics                          
  • Auto Backup and Recovery                                 
                                                              
================================================================
        """
        print(banner)
        print(f"Launch Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
    
    def check_python_version(self):
        """Check Python version"""
        print("Checking Python version...")
        current_version = sys.version_info[:2]
        
        if current_version >= self.required_python:
            print(f"[OK] Python version: {sys.version.split()[0]} (Compatible)")
            return True
        else:
            print(f"[ERROR] Python version too old: {sys.version.split()[0]}")
            print(f"        Requires Python {self.required_python[0]}.{self.required_python[1]}+")
            return False
    
    def check_dependencies(self):
        """Check dependencies"""
        print("\nChecking dependencies...")
        missing_deps = []
        optional_missing = []
        
        # Check required dependencies
        for package, import_name in self.dependencies.items():
            try:
                __import__(import_name)
                print(f"[OK] {package}")
            except ImportError:
                print(f"[MISSING] {package}")
                missing_deps.append(package)
        
        # Check optional dependencies
        for package, import_name in self.optional_dependencies.items():
            try:
                __import__(import_name)
                print(f"[OK] {package} (optional)")
            except ImportError:
                print(f"[MISSING] {package} (optional)")
                optional_missing.append(package)
        
        return missing_deps, optional_missing
    
    def install_dependencies(self, missing_deps):
        """Install missing dependencies"""
        if not missing_deps:
            return True
        
        print(f"\nNeed to install {len(missing_deps)} packages:")
        for dep in missing_deps:
            print(f"   • {dep}")
        
        try:
            response = input("\nInstall these packages now? (y/n): ").lower().strip()
            if response != 'y':
                print("Skipping dependency installation")
                return False
        except KeyboardInterrupt:
            print("\nInstallation cancelled by user")
            return False
        
        print("\nInstalling dependencies...")
        success_count = 0
        
        for package in missing_deps:
            print(f"Installing {package}...")
            try:
                result = subprocess.run(
                    [sys.executable, "-m", "pip", "install", package],
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 minute timeout
                )
                
                if result.returncode == 0:
                    print(f"[OK] {package} installed successfully")
                    success_count += 1
                else:
                    print(f"[ERROR] {package} installation failed:")
                    print(f"        {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print(f"[ERROR] {package} installation timeout")
            except Exception as e:
                print(f"[ERROR] {package} installation exception: {e}")
        
        print(f"\nInstallation result: {success_count}/{len(missing_deps)} packages installed")
        return success_count == len(missing_deps)
    
    def check_environment(self):
        """Check runtime environment"""
        print("\nChecking runtime environment...")
        
        # Check required directories
        required_dirs = ['configs', 'templates', 'logs', 'data', 'backups']
        for dir_name in required_dirs:
            dir_path = Path(dir_name)
            if not dir_path.exists():
                dir_path.mkdir(exist_ok=True)
                print(f"[CREATED] Directory: {dir_name}")
            else:
                print(f"[OK] Directory exists: {dir_name}")
        
        # Check core modules
        print("\nChecking core modules...")
        core_modules = [
            'modules/config_manager.py',
            'modules/image_recognition.py',
            'modules/hotkey_manager.py',
            'modules/skill_monitor.py',
            'modules/health_monitor.py',
            'modules/ui_manager.py'
        ]
        
        missing_modules = []
        for module_path in core_modules:
            if Path(module_path).exists():
                print(f"[OK] {module_path}")
            else:
                print(f"[MISSING] {module_path}")
                missing_modules.append(module_path)
        
        if missing_modules:
            print(f"\nWarning: Missing {len(missing_modules)} core modules")
            print("Program may not work correctly")
            return False
        
        return True
    
    def show_launch_options(self):
        """Show launch options"""
        print("\nLaunch Options:")
        print("1. Full Version (all features)")
        print("2. Simple Version (basic features)")
        print("3. English Version (English UI)")
        print("4. Test Mode (testing only)")
        print("5. Configuration Wizard")
        print("6. Feature Demo")
        print("7. Exit")
        print()
        
        while True:
            try:
                choice = input("Please select launch option (1-7): ").strip()
                if choice in ['1', '2', '3', '4', '5', '6', '7']:
                    return choice
                else:
                    print("Invalid choice, please enter 1-7")
            except KeyboardInterrupt:
                print("\nCancelled by user")
                return '7'
    
    def launch_application(self, choice):
        """Launch application"""
        print(f"\nLaunching application...")
        
        try:
            if choice == '1':
                print("Starting full version...")
                if Path("XXD.py").exists():
                    subprocess.run([sys.executable, "XXD.py"])
                else:
                    print("[ERROR] XXD.py not found")
                
            elif choice == '2':
                print("Starting simple version...")
                if Path("XXD_simple.py").exists():
                    subprocess.run([sys.executable, "XXD_simple.py"])
                else:
                    print("[ERROR] XXD_simple.py not found")
                
            elif choice == '3':
                print("Starting English version...")
                if Path("XXD_en.py").exists():
                    subprocess.run([sys.executable, "XXD_en.py"])
                else:
                    print("[ERROR] XXD_en.py not found")
                
            elif choice == '4':
                print("Starting test mode...")
                if Path("test_basic.py").exists():
                    subprocess.run([sys.executable, "test_basic.py"])
                else:
                    print("[ERROR] test_basic.py not found")
                
            elif choice == '5':
                print("Starting configuration wizard...")
                self.run_config_wizard()
                
            elif choice == '6':
                print("Starting feature demo...")
                if Path("demo_features.py").exists():
                    subprocess.run([sys.executable, "demo_features.py"])
                else:
                    print("[ERROR] demo_features.py not found")
                
            elif choice == '7':
                print("Exiting program")
                return
                
        except FileNotFoundError as e:
            print(f"[ERROR] File not found: {e}")
            print("Please ensure all necessary files exist")
        except Exception as e:
            print(f"[ERROR] Launch failed: {e}")
    
    def run_config_wizard(self):
        """Run configuration wizard"""
        print("\nConfiguration Wizard")
        print("=" * 50)
        
        try:
            # Get configuration info
            config_name = input("Enter configuration name (e.g., MyDruid): ").strip()
            if not config_name:
                config_name = f"Config_{int(time.time())}"
            
            print(f"\nCreating configuration: {config_name}")
            
            # Create basic configuration
            config_data = {
                "monitor_region": [1193, 755, 50, 50],
                "health_region": [500, 100, 200, 30],
                "settings": {
                    "scan_interval": 0.33,
                    "threshold": 0.9,
                    "key_press_delay": 0.19,
                    "health_scan_interval": 0.3,
                    "priority_mode": "health_first"
                },
                "icon_bindings": {},
                "health_actions": {
                    "30": {"hotkey": "h", "description": "Use healing stone"},
                    "20": {"hotkey": "s", "description": "Use survival skill"}
                }
            }
            
            # Save configuration
            config_path = Path("configs") / f"{config_name}.json"
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            print(f"[OK] Configuration saved: {config_path}")
            print("\nConfiguration wizard completed!")
            print("You can now start the program and load this configuration")
            
        except Exception as e:
            print(f"[ERROR] Configuration wizard failed: {e}")
    
    def run(self):
        """Run launcher"""
        try:
            self.print_banner()
            
            # Check Python version
            if not self.check_python_version():
                input("\nPress Enter to exit...")
                return
            
            # Check dependencies
            missing_deps, optional_missing = self.check_dependencies()
            
            # Install missing dependencies
            if missing_deps:
                if not self.install_dependencies(missing_deps):
                    print("\nWarning: Some dependencies not installed, program may not work correctly")
                    response = input("Continue anyway? (y/n): ").lower().strip()
                    if response != 'y':
                        return
            
            # Check environment
            if not self.check_environment():
                print("\nEnvironment check failed, but you can still try to run")
            
            # Show launch options
            choice = self.show_launch_options()
            
            # Launch application
            if choice != '7':
                self.launch_application(choice)
            
        except KeyboardInterrupt:
            print("\n\nProgram interrupted by user")
        except Exception as e:
            print(f"\n[ERROR] Launcher failed: {e}")
            import traceback
            traceback.print_exc()
        finally:
            print("\nThank you for using Mengzi WoW Assistant!")


def main():
    """Main function"""
    launcher = MengziLauncher()
    launcher.run()


if __name__ == "__main__":
    main()
