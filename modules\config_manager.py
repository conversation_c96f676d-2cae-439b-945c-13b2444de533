"""
配置管理模块
负责管理多职业配置文件的创建、加载、保存和切换
"""

import json
import os
from typing import Dict, Any, Optional
import logging

class ConfigManager:
    def __init__(self, config_dir: str = "configs"):
        self.config_dir = config_dir
        self.current_config = None
        self.current_config_name = None
        self.default_config = self._get_default_config()
        
        # 确保配置目录存在
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
            
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "monitor_region": [1193, 755, 50, 50],  # [x, y, width, height]
            "health_region": [500, 100, 200, 30],   # 血条区域
            "settings": {
                "monitor_hotkey": "`",
                "threshold": 0.9,
                "scan_interval": 0.33,
                "key_press_delay": 0.19,
                "auto_add_skills": True,
                "health_threshold": 30,
                "health_scan_interval": 0.3,
                "priority_mode": "health_first",  # health_first, skill_first, balanced
                "resource_limit": 50,  # CPU使用率限制
                "adaptive_scan": True   # 自适应扫描频率
            },
            "icon_bindings": {},
            "health_actions": {
                "30": {"hotkey": "h", "description": "使用治疗石"},
                "20": {"hotkey": "s", "description": "使用保命技能"},
                "10": {"hotkey": "x", "description": "紧急逃脱"}
            },
            "hotkeys": {
                "toggle_monitor": "`",
                "toggle_health": "F8",
                "toggle_auto_add": "F9",
                "quick_add_skill": "F10",
                "set_region": "F11",
                "exit_program": "F12",
                "set_health_region": "ctrl+h"
            }
        }
    
    def create_config(self, config_name: str) -> bool:
        """创建新配置"""
        try:
            config_path = os.path.join(self.config_dir, f"{config_name}.json")
            if os.path.exists(config_path):
                self.logger.warning(f"配置 {config_name} 已存在")
                return False
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.default_config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置 {config_name} 创建成功")
            return True
        except Exception as e:
            self.logger.error(f"创建配置失败: {e}")
            return False
    
    def load_config(self, config_name: str) -> bool:
        """加载配置"""
        try:
            config_path = os.path.join(self.config_dir, f"{config_name}.json")
            if not os.path.exists(config_path):
                self.logger.error(f"配置文件 {config_name} 不存在")
                return False
            
            with open(config_path, 'r', encoding='utf-8') as f:
                self.current_config = json.load(f)
            
            self.current_config_name = config_name
            self.logger.info(f"配置 {config_name} 加载成功")
            return True
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """保存当前配置"""
        if not self.current_config or not self.current_config_name:
            self.logger.error("没有当前配置可保存")
            return False
        
        try:
            config_path = os.path.join(self.config_dir, f"{self.current_config_name}.json")
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.current_config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置 {self.current_config_name} 保存成功")
            return True
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            return False
    
    def get_config_list(self) -> list:
        """获取所有配置文件列表"""
        configs = []
        for file in os.listdir(self.config_dir):
            if file.endswith('.json'):
                configs.append(file[:-5])  # 移除.json后缀
        return configs
    
    def get_setting(self, key: str, default=None):
        """获取设置值"""
        if not self.current_config:
            return default
        return self.current_config.get("settings", {}).get(key, default)
    
    def set_setting(self, key: str, value: Any):
        """设置配置值"""
        if not self.current_config:
            self.current_config = self.default_config.copy()
        
        if "settings" not in self.current_config:
            self.current_config["settings"] = {}
        
        self.current_config["settings"][key] = value
    
    def get_monitor_region(self) -> list:
        """获取监控区域"""
        if not self.current_config:
            return self.default_config["monitor_region"]
        return self.current_config.get("monitor_region", self.default_config["monitor_region"])
    
    def set_monitor_region(self, region: list):
        """设置监控区域"""
        if not self.current_config:
            self.current_config = self.default_config.copy()
        self.current_config["monitor_region"] = region
    
    def get_health_region(self) -> list:
        """获取血条区域"""
        if not self.current_config:
            return self.default_config["health_region"]
        return self.current_config.get("health_region", self.default_config["health_region"])
    
    def set_health_region(self, region: list):
        """设置血条区域"""
        if not self.current_config:
            self.current_config = self.default_config.copy()
        self.current_config["health_region"] = region
    
    def get_icon_bindings(self) -> Dict[str, Any]:
        """获取技能图标绑定"""
        if not self.current_config:
            return {}
        return self.current_config.get("icon_bindings", {})
    
    def add_icon_binding(self, icon_id: str, hotkey: str, template_path: str, text: str = ""):
        """添加技能图标绑定"""
        if not self.current_config:
            self.current_config = self.default_config.copy()
        
        if "icon_bindings" not in self.current_config:
            self.current_config["icon_bindings"] = {}
        
        self.current_config["icon_bindings"][icon_id] = {
            "hotkey": hotkey,
            "template_path": template_path,
            "text": text
        }
    
    def get_health_actions(self) -> Dict[str, Any]:
        """获取血量应对措施"""
        if not self.current_config:
            return self.default_config["health_actions"]
        return self.current_config.get("health_actions", self.default_config["health_actions"])
    
    def set_health_action(self, threshold: str, hotkey: str, description: str):
        """设置血量应对措施"""
        if not self.current_config:
            self.current_config = self.default_config.copy()
        
        if "health_actions" not in self.current_config:
            self.current_config["health_actions"] = {}
        
        self.current_config["health_actions"][threshold] = {
            "hotkey": hotkey,
            "description": description
        }
