# 孟子 - 魔兽世界智能助手项目说明

## 项目概述

本项目是一个完整的魔兽世界智能助手应用，名为"孟子"，版本2.0。该应用通过计算机视觉技术监控游戏界面，实现技能自动施放和血量监控功能。

## 项目特点

### 技术架构
- **编程语言**: Python 3.8+
- **UI框架**: tkinter (跨平台GUI)
- **图像处理**: OpenCV (计算机视觉)
- **自动化**: pyautogui, keyboard, pynput
- **配置管理**: JSON格式配置文件
- **模块化设计**: 清晰的模块分离和职责划分

### 核心功能
1. **技能监控系统**
   - 监控Hekili等插件的技能建议区域
   - 基于模板匹配的图像识别
   - 自动按键执行技能
   - 支持多技能绑定和管理

2. **血量监控系统**
   - 实时监控角色血量百分比
   - 多阈值血量应对措施
   - 智能冷却时间管理
   - 血量趋势分析

3. **协同工作机制**
   - 技能监控与血量监控协同运作
   - 智能优先级调度
   - 多种工作模式切换
   - 资源使用优化

4. **用户界面**
   - 直观的图形化配置界面
   - 实时状态显示
   - 详细的统计信息
   - 多选项卡布局

## 文件结构

```
WOW/
├── XXD.py                    # 主程序入口
├── start.bat                 # Windows启动脚本
├── test_imports.py           # 模块导入测试脚本
├── requirements.txt          # Python依赖包列表
├── README.md                 # 项目说明文档
├── 使用指南.md               # 详细使用指南
├── 项目说明.md               # 本文件
├── modules/                  # 核心模块目录
│   ├── __init__.py          # 模块包初始化
│   ├── config_manager.py    # 配置管理模块
│   ├── image_recognition.py # 图像识别模块
│   ├── hotkey_manager.py    # 热键管理模块
│   ├── skill_monitor.py     # 技能监控模块
│   ├── health_monitor.py    # 血量监控模块
│   └── ui_manager.py        # UI界面管理模块
├── configs/                  # 配置文件目录
│   └── NIAODE01.json        # 示例配置文件
├── templates/                # 技能图标模板目录
└── logs/                     # 日志文件目录
```

## 模块详细说明

### 1. config_manager.py - 配置管理模块
- **功能**: 管理多职业配置文件的创建、加载、保存
- **特点**: JSON格式配置，支持配置切换和备份
- **主要类**: ConfigManager

### 2. image_recognition.py - 图像识别模块
- **功能**: 基于OpenCV的图像处理和模板匹配
- **特点**: 支持多种血量检测方法，图像增强处理
- **主要类**: ImageRecognition

### 3. hotkey_manager.py - 热键管理模块
- **功能**: 全局热键监听和按键模拟
- **特点**: 支持组合键，异步按键，紧急停止
- **主要类**: HotkeyManager

### 4. skill_monitor.py - 技能监控模块
- **功能**: 监控技能建议区域，自动执行技能
- **特点**: 多技能绑定，统计分析，自动添加技能
- **主要类**: SkillMonitor

### 5. health_monitor.py - 血量监控模块
- **功能**: 实时血量监控和应急措施触发
- **特点**: 多阈值设置，血量趋势分析，智能优先级
- **主要类**: HealthMonitor

### 6. ui_manager.py - UI界面管理模块
- **功能**: 图形化用户界面和交互逻辑
- **特点**: 多选项卡界面，实时更新，直观配置
- **主要类**: UIManager

## 技术实现亮点

### 1. 模块化架构
- 清晰的职责分离
- 松耦合设计
- 易于扩展和维护

### 2. 智能协同机制
- 技能监控与血量监控协同工作
- 动态优先级调整
- 资源使用优化

### 3. 用户体验优化
- 直观的图形界面
- 详细的使用指南
- 完善的错误处理

### 4. 安全性考虑
- 紧急停止机制
- 资源使用限制
- 详细的日志记录

## 使用场景

### 适用职业
- 所有魔兽世界职业和专精
- 特别适合需要复杂技能循环的职业
- 支持多套配置快速切换

### 适用内容
- 日常任务和升级
- 副本和团队副本
- PvP战场和竞技场
- 各种游戏活动

## 安装和使用

### 系统要求
- Windows 10/11
- Python 3.8或更高版本
- 4GB以上内存
- 建议1920x1080分辨率

### 快速开始
1. 双击运行 `start.bat`
2. 程序自动检查和安装依赖
3. 创建或加载配置文件
4. 设置监控区域
5. 开始监控

### 详细配置
请参考 `使用指南.md` 文件中的详细说明。

## 开发特色

### 1. 完整性
- 从核心功能到用户界面的完整实现
- 详细的文档和使用指南
- 示例配置和测试脚本

### 2. 专业性
- 规范的代码结构和注释
- 完善的错误处理机制
- 详细的日志记录系统

### 3. 实用性
- 针对实际游戏需求设计
- 考虑了各种使用场景
- 提供了丰富的配置选项

### 4. 可扩展性
- 模块化设计便于功能扩展
- 配置文件支持自定义参数
- 预留了多种扩展接口

## 注意事项

### 使用风险
- 本程序仅供学习研究使用
- 使用可能违反游戏服务条款
- 用户需自行承担使用风险

### 技术限制
- 依赖于游戏界面的稳定性
- 需要合适的显示设置
- 对系统性能有一定要求

### 建议
- 在测试环境中充分验证
- 合理设置监控参数
- 定期备份配置文件
- 遵守相关法律法规

## 总结

本项目实现了一个功能完整、设计合理的魔兽世界智能助手应用。通过模块化的架构设计，实现了技能监控、血量监控、用户界面等核心功能。项目代码规范，文档完善，具有良好的可维护性和可扩展性。

该应用不仅满足了用户的实际需求，还展示了计算机视觉、自动化控制、GUI开发等多种技术的综合应用。是一个优秀的Python项目实践案例。
