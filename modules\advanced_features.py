"""
高级功能模块
包含智能学习、性能优化、数据分析等高级功能
"""

import json
import time
import threading
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import psutil

class AdvancedAnalytics:
    """高级数据分析类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.data_dir = Path("data/analytics")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # 分析数据
        self.skill_efficiency = {}
        self.health_patterns = {}
        self.combat_analysis = {}
        
    def analyze_skill_efficiency(self, skill_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """分析技能使用效率"""
        try:
            efficiency_scores = {}
            
            for skill_id, usages in self._group_by_skill(skill_data).items():
                # 计算技能使用频率
                frequency = len(usages)
                
                # 计算平均置信度
                avg_confidence = sum(usage.get('confidence', 0) for usage in usages) / len(usages)
                
                # 计算成功率（基于置信度）
                success_rate = len([u for u in usages if u.get('confidence', 0) > 0.8]) / len(usages)
                
                # 综合效率分数
                efficiency_scores[skill_id] = {
                    'frequency': frequency,
                    'avg_confidence': avg_confidence,
                    'success_rate': success_rate,
                    'efficiency_score': (avg_confidence * success_rate * min(frequency / 10, 1.0))
                }
            
            return efficiency_scores
            
        except Exception as e:
            self.logger.error(f"技能效率分析失败: {e}")
            return {}
    
    def analyze_health_patterns(self, health_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析血量变化模式"""
        try:
            if not health_data:
                return {}
            
            # 按时间排序
            sorted_data = sorted(health_data, key=lambda x: x.get('timestamp', 0))
            
            # 分析血量趋势
            health_values = [d.get('health_percent', 100) for d in sorted_data]
            
            # 计算统计指标
            avg_health = sum(health_values) / len(health_values)
            min_health = min(health_values)
            max_health = max(health_values)
            
            # 计算血量波动性
            volatility = self._calculate_volatility(health_values)
            
            # 识别危险时段
            danger_periods = self._identify_danger_periods(sorted_data)
            
            return {
                'avg_health': avg_health,
                'min_health': min_health,
                'max_health': max_health,
                'volatility': volatility,
                'danger_periods': danger_periods,
                'total_samples': len(health_data)
            }
            
        except Exception as e:
            self.logger.error(f"血量模式分析失败: {e}")
            return {}
    
    def _group_by_skill(self, skill_data: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按技能ID分组"""
        groups = {}
        for usage in skill_data:
            skill_id = usage.get('skill_id', 'unknown')
            if skill_id not in groups:
                groups[skill_id] = []
            groups[skill_id].append(usage)
        return groups
    
    def _calculate_volatility(self, values: List[float]) -> float:
        """计算波动性"""
        if len(values) < 2:
            return 0
        
        avg = sum(values) / len(values)
        variance = sum((x - avg) ** 2 for x in values) / len(values)
        return variance ** 0.5
    
    def _identify_danger_periods(self, sorted_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """识别危险时段"""
        danger_periods = []
        danger_threshold = 30  # 血量低于30%视为危险
        
        in_danger = False
        danger_start = None
        
        for data_point in sorted_data:
            health = data_point.get('health_percent', 100)
            timestamp = data_point.get('timestamp', 0)
            
            if health < danger_threshold and not in_danger:
                # 进入危险期
                in_danger = True
                danger_start = timestamp
            elif health >= danger_threshold and in_danger:
                # 脱离危险期
                in_danger = False
                if danger_start:
                    danger_periods.append({
                        'start_time': danger_start,
                        'end_time': timestamp,
                        'duration': timestamp - danger_start,
                        'min_health': min(d.get('health_percent', 100) 
                                        for d in sorted_data 
                                        if danger_start <= d.get('timestamp', 0) <= timestamp)
                    })
        
        return danger_periods
    
    def generate_recommendations(self, analysis_data: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        try:
            # 基于技能效率的建议
            skill_efficiency = analysis_data.get('skill_efficiency', {})
            if skill_efficiency:
                low_efficiency_skills = [
                    skill_id for skill_id, data in skill_efficiency.items()
                    if data.get('efficiency_score', 0) < 0.5
                ]
                
                if low_efficiency_skills:
                    recommendations.append(
                        f"建议检查以下技能的配置: {', '.join(low_efficiency_skills[:3])}"
                    )
            
            # 基于血量模式的建议
            health_patterns = analysis_data.get('health_patterns', {})
            if health_patterns:
                avg_health = health_patterns.get('avg_health', 100)
                volatility = health_patterns.get('volatility', 0)
                
                if avg_health < 60:
                    recommendations.append("平均血量较低，建议增加治疗技能或调整血量阈值")
                
                if volatility > 20:
                    recommendations.append("血量波动较大，建议优化血量监控频率")
                
                danger_periods = health_patterns.get('danger_periods', [])
                if len(danger_periods) > 5:
                    recommendations.append("危险时段较多，建议提高血量预警阈值")
            
            # 性能相关建议
            if 'performance' in analysis_data:
                perf_data = analysis_data['performance']
                if perf_data.get('avg_cpu', 0) > 70:
                    recommendations.append("CPU使用率较高，建议增加扫描间隔或减小监控区域")
                
                if perf_data.get('avg_memory', 0) > 80:
                    recommendations.append("内存使用率较高，建议重启程序或清理历史数据")
            
            if not recommendations:
                recommendations.append("当前配置运行良好，无需调整")
            
        except Exception as e:
            self.logger.error(f"生成建议失败: {e}")
            recommendations.append("分析数据不足，无法生成建议")
        
        return recommendations


class SmartOptimizer:
    """智能优化器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.optimization_history = []
        
    def optimize_scan_intervals(self, performance_data: Dict[str, float], 
                              current_intervals: Dict[str, float]) -> Dict[str, float]:
        """优化扫描间隔"""
        try:
            optimized_intervals = current_intervals.copy()
            
            cpu_usage = performance_data.get('cpu_usage', 0)
            memory_usage = performance_data.get('memory_usage', 0)
            
            # 基于CPU使用率调整
            if cpu_usage > 80:
                # CPU使用率过高，增加间隔
                for key in optimized_intervals:
                    optimized_intervals[key] = min(optimized_intervals[key] * 1.2, 2.0)
                self.logger.info("CPU使用率过高，增加扫描间隔")
                
            elif cpu_usage < 30:
                # CPU使用率较低，可以减少间隔提高响应速度
                for key in optimized_intervals:
                    optimized_intervals[key] = max(optimized_intervals[key] * 0.9, 0.1)
                self.logger.info("CPU使用率较低，减少扫描间隔")
            
            # 基于内存使用率调整
            if memory_usage > 85:
                # 内存使用率过高，增加间隔
                for key in optimized_intervals:
                    optimized_intervals[key] = min(optimized_intervals[key] * 1.1, 1.5)
                self.logger.warning("内存使用率过高，增加扫描间隔")
            
            return optimized_intervals
            
        except Exception as e:
            self.logger.error(f"优化扫描间隔失败: {e}")
            return current_intervals
    
    def optimize_thresholds(self, accuracy_data: Dict[str, float]) -> Dict[str, float]:
        """优化匹配阈值"""
        try:
            optimized_thresholds = {}
            
            for skill_id, accuracy in accuracy_data.items():
                if accuracy > 0.95:
                    # 准确率很高，可以降低阈值提高响应速度
                    optimized_thresholds[skill_id] = max(0.8, accuracy - 0.1)
                elif accuracy < 0.7:
                    # 准确率较低，提高阈值减少误触发
                    optimized_thresholds[skill_id] = min(0.95, accuracy + 0.15)
                else:
                    # 准确率适中，保持当前阈值
                    optimized_thresholds[skill_id] = accuracy
            
            return optimized_thresholds
            
        except Exception as e:
            self.logger.error(f"优化匹配阈值失败: {e}")
            return {}


class DataExporter:
    """数据导出器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.export_dir = Path("exports")
        self.export_dir.mkdir(exist_ok=True)
    
    def export_session_data(self, session_data: Dict[str, Any], format_type: str = "json") -> str:
        """导出会话数据"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if format_type.lower() == "json":
                filename = f"session_data_{timestamp}.json"
                filepath = self.export_dir / filename
                
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(session_data, f, indent=2, ensure_ascii=False, default=str)
                
            elif format_type.lower() == "csv":
                filename = f"session_data_{timestamp}.csv"
                filepath = self.export_dir / filename
                
                self._export_to_csv(session_data, filepath)
            
            else:
                raise ValueError(f"不支持的导出格式: {format_type}")
            
            self.logger.info(f"会话数据导出完成: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"导出会话数据失败: {e}")
            return ""
    
    def _export_to_csv(self, data: Dict[str, Any], filepath: Path):
        """导出为CSV格式"""
        import csv
        
        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # 写入标题行
            writer.writerow(['Category', 'Key', 'Value'])
            
            # 递归写入数据
            self._write_dict_to_csv(writer, data, '')
    
    def _write_dict_to_csv(self, writer, data: Any, prefix: str = ''):
        """递归写入字典数据到CSV"""
        if isinstance(data, dict):
            for key, value in data.items():
                new_prefix = f"{prefix}.{key}" if prefix else key
                if isinstance(value, (dict, list)):
                    self._write_dict_to_csv(writer, value, new_prefix)
                else:
                    writer.writerow([prefix, key, value])
        elif isinstance(data, list):
            for i, item in enumerate(data):
                new_prefix = f"{prefix}[{i}]"
                self._write_dict_to_csv(writer, item, new_prefix)
        else:
            writer.writerow([prefix, '', data])
