#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有模块的导入
"""

import sys
import traceback

def test_import(module_name, package=None):
    """测试模块导入"""
    try:
        if package:
            __import__(f"{package}.{module_name}")
            print(f"✓ {package}.{module_name} 导入成功")
        else:
            __import__(module_name)
            print(f"✓ {module_name} 导入成功")
        return True
    except Exception as e:
        print(f"✗ {package}.{module_name if package else module_name} 导入失败: {e}")
        traceback.print_exc()
        return False

def main():
    print("孟子智能助手 - 模块导入测试")
    print("=" * 40)
    
    # 测试标准库
    print("\n测试标准库:")
    standard_libs = [
        'os', 'sys', 'json', 'time', 'threading', 'logging', 'pathlib'
    ]
    
    for lib in standard_libs:
        test_import(lib)
    
    # 测试第三方库
    print("\n测试第三方库:")
    third_party_libs = [
        'cv2', 'numpy', 'PIL', 'keyboard', 'pyautogui', 'psutil'
    ]
    
    for lib in third_party_libs:
        test_import(lib)
    
    # 测试tkinter
    print("\n测试tkinter:")
    try:
        import tkinter as tk
        from tkinter import ttk, messagebox, filedialog, simpledialog
        print("✓ tkinter 及相关模块导入成功")
    except Exception as e:
        print(f"✗ tkinter 导入失败: {e}")
    
    # 测试自定义模块
    print("\n测试自定义模块:")
    custom_modules = [
        'config_manager', 'image_recognition', 'hotkey_manager',
        'skill_monitor', 'health_monitor', 'ui_manager'
    ]
    
    for module in custom_modules:
        test_import(module, 'modules')
    
    print("\n" + "=" * 40)
    print("模块导入测试完成")

if __name__ == "__main__":
    main()
