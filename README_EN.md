# Mengzi - World of Warcraft Assistant v2.0

## Overview

Mengzi is an intelligent assistant designed specifically for World of Warcraft. It monitors skill suggestion areas and health status in the game interface through computer vision technology, enabling automatic skill casting and health warning functions. This program is based on computer vision technology, does not modify game files, will not be detected as a cheat, and provides players with a smoother gaming experience.

## Key Features

### Core Functions
- **Skill Monitoring & Auto-casting**: Monitor skill suggestion areas from plugins like Hekili, automatically press corresponding keys
- **Health Monitoring System**: Real-time monitoring of character health, automatically trigger response measures when in danger
- **Dual-area Coordination**: Skill area and health area monitoring simultaneously, intelligent decision-making for optimal operations

### Auxiliary Functions
- **Multi-class Configuration Support**: Save multiple class/specialization configuration files, one-click switching
- **Custom Key Bindings**: Set corresponding keyboard keys for each skill icon and health threshold
- **Visual Interface**: Intuitive user interface, easy to configure and use
- **Real-time Preview**: Display real-time preview of current monitoring areas
- **Intelligent Priority**: Set health priority, prioritize life-saving skills when in danger

## System Requirements

- Windows 10/11
- Python 3.8+
- Resolution: Recommended 1920x1080 or higher
- CPU: Recommended Intel i5/AMD Ryzen 5 or higher
- Memory: 4GB or more

## Installation Guide

### Quick Start (Recommended)

1. **Download and extract** all files to a folder
2. **Double-click** `run.bat` for quick start
3. **Choose option 1** for simple version (no dependencies needed)
4. **Choose option 2** for full version (requires packages)

### Manual Installation

1. Ensure Python 3.8 or higher is installed
2. Install required dependencies:
   ```bash
   pip install opencv-python numpy Pillow keyboard pyautogui psutil pynput
   ```
3. Run the main program:
   ```bash
   python XXD.py
   ```

### If You Have Problems

1. **Test basic environment**:
   ```bash
   python test_basic.py
   ```

2. **Try simple version first**:
   ```bash
   python XXD_en.py
   ```

3. **Use auto-fix script**:
   ```bash
   fix_and_run.bat
   ```

## Quick Start Guide

### Basic Setup
1. **Create Configuration**:
   - Click "Create New" button, enter configuration name (e.g., "MyDruid")
   - System will automatically create configuration file with default settings

2. **Set Monitoring Areas**:
   - Click "Set Skill Area" to select skill suggestion position in game
   - Use arrow keys to fine-tune position, press Enter to confirm

3. **Set Health Bar Area**:
   - Click "Set Health Area" to select health bar position in game
   - Use arrow keys to fine-tune position, press Enter to confirm

4. **Start Monitoring**:
   - Click "Start Monitor" button, program will monitor both skills and health
   - Use hotkey "~" to quickly start/stop monitoring

## File Structure

```
WOW/
├── XXD.py                    # Main program (full version)
├── XXD_en.py                 # English simple version
├── XXD_simple.py             # Chinese simple version
├── run.bat                   # Quick start script
├── start.bat                 # Advanced start script
├── fix_and_run.bat          # Auto-fix script
├── test_basic.py            # Basic environment test
├── requirements.txt         # Python dependencies
├── README.md                # Chinese documentation
├── README_EN.md             # This file
├── modules/                 # Core modules
│   ├── config_manager.py    # Configuration management
│   ├── image_recognition.py # Image recognition
│   ├── hotkey_manager.py    # Hotkey management
│   ├── skill_monitor.py     # Skill monitoring
│   ├── health_monitor.py    # Health monitoring
│   └── ui_manager.py        # UI interface
├── configs/                 # Configuration files
├── templates/               # Skill icon templates
└── logs/                    # Log files
```

## Version Comparison

### Simple Version (XXD_en.py)
- ✅ Basic interface and configuration management
- ✅ No external dependencies required
- ✅ Good for testing Python environment
- ❌ No image recognition functionality
- ❌ No actual monitoring capabilities

### Full Version (XXD.py)
- ✅ Complete skill monitoring with image recognition
- ✅ Real-time health monitoring
- ✅ Global hotkey support
- ✅ Advanced coordination features
- ❌ Requires additional Python packages

## Troubleshooting

### Common Issues

1. **"System cannot find the specified path"**
   - Usually caused by missing dependencies
   - Solution: Run `pip install opencv-python numpy Pillow keyboard pyautogui psutil pynput`

2. **Chinese characters appear as garbled text**
   - Use English versions: `XXD_en.py` or `run.bat`
   - All functionality is the same, just in English

3. **Program starts but immediately closes**
   - Check Python version: `python --version`
   - Test basic environment: `python test_basic.py`
   - Try simple version first: `python XXD_en.py`

4. **tkinter not available**
   - Reinstall Python with tkinter included
   - On Linux: `sudo apt-get install python3-tk`

### Step-by-Step Diagnosis

1. **Test Python environment**:
   ```bash
   python test_basic.py
   ```

2. **If basic test passes, try simple version**:
   ```bash
   python XXD_en.py
   ```

3. **If simple version works, install dependencies**:
   ```bash
   pip install opencv-python numpy Pillow keyboard pyautogui psutil pynput
   ```

4. **Then try full version**:
   ```bash
   python XXD.py
   ```

## Usage Tips

### For Beginners
- Start with the simple version to test your environment
- Use `run.bat` for easy startup options
- Read the in-program instructions carefully

### For Advanced Users
- Customize configurations for different classes
- Adjust monitoring parameters for optimal performance
- Use the full version for complete automation

### Safety Recommendations
- Test in safe environments first
- Don't rely completely on automation
- Be familiar with emergency stop procedures
- Understand game terms of service

## Support

### Getting Help
1. **Check logs**: Look in `logs/` folder for error messages
2. **Test environment**: Run `test_basic.py` for diagnosis
3. **Try simple version**: Use `XXD_en.py` to isolate issues
4. **Read documentation**: Check all .md files for detailed info

### Reporting Issues
When reporting problems, please include:
- Python version: `python --version`
- Operating system version
- Complete error messages
- Steps to reproduce the issue

## Disclaimer

This program is for educational and research purposes only. Using this program may violate game terms of service. Users assume all risks. The developer is not responsible for any consequences resulting from the use of this program.

## License

This project is for educational purposes. Please respect game terms of service and applicable laws.
