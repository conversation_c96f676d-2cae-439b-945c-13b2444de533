#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
孟子 - 魔兽世界智能助手 v2.0 (简化版)
主程序入口 - 仅使用标准库和tkinter
"""

import sys
import os
import json
import logging
import threading
import time
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
from pathlib import Path

class SimpleConfigManager:
    """简化的配置管理器"""
    
    def __init__(self, config_dir="configs"):
        self.config_dir = config_dir
        self.current_config = None
        self.current_config_name = None
        
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            "monitor_region": [1193, 755, 50, 50],
            "health_region": [500, 100, 200, 30],
            "settings": {
                "scan_interval": 0.33,
                "threshold": 0.9,
                "key_press_delay": 0.19
            },
            "icon_bindings": {},
            "health_actions": {
                "30": {"hotkey": "h", "description": "使用治疗石"},
                "20": {"hotkey": "s", "description": "使用保命技能"}
            }
        }
    
    def create_config(self, config_name):
        """创建新配置"""
        try:
            config_path = os.path.join(self.config_dir, f"{config_name}.json")
            if os.path.exists(config_path):
                return False
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.get_default_config(), f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"创建配置失败: {e}")
            return False
    
    def load_config(self, config_name):
        """加载配置"""
        try:
            config_path = os.path.join(self.config_dir, f"{config_name}.json")
            if not os.path.exists(config_path):
                return False
            
            with open(config_path, 'r', encoding='utf-8') as f:
                self.current_config = json.load(f)
            
            self.current_config_name = config_name
            return True
        except Exception as e:
            print(f"加载配置失败: {e}")
            return False
    
    def get_config_list(self):
        """获取配置列表"""
        configs = []
        for file in os.listdir(self.config_dir):
            if file.endswith('.json'):
                configs.append(file[:-5])
        return configs

class SimpleUI:
    """简化的用户界面"""
    
    def __init__(self):
        self.config_manager = SimpleConfigManager()
        self.root = None
        self.is_monitoring = False
        
    def create_window(self):
        """创建主窗口"""
        self.root = tk.Tk()
        self.root.title("孟子 - 魔兽世界智能助手 v2.0 (简化版)")
        self.root.geometry("600x400")
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = tk.Label(main_frame, text="孟子 - 魔兽世界智能助手", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="状态信息")
        status_frame.pack(fill=tk.X, pady=5)
        
        self.status_var = tk.StringVar(value="未监控")
        status_label = tk.Label(status_frame, textvariable=self.status_var, 
                               font=("Arial", 12))
        status_label.pack(pady=5)
        
        # 配置管理
        config_frame = ttk.LabelFrame(main_frame, text="配置管理")
        config_frame.pack(fill=tk.X, pady=5)
        
        # 配置选择
        config_select_frame = ttk.Frame(config_frame)
        config_select_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(config_select_frame, text="配置:").pack(side=tk.LEFT)
        
        self.config_combo = ttk.Combobox(config_select_frame, width=20)
        self.config_combo.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(config_select_frame, text="刷新", 
                  command=self.refresh_configs).pack(side=tk.LEFT, padx=2)
        ttk.Button(config_select_frame, text="加载", 
                  command=self.load_config).pack(side=tk.LEFT, padx=2)
        
        # 配置操作
        config_ops_frame = ttk.Frame(config_frame)
        config_ops_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(config_ops_frame, text="创建新配置", 
                  command=self.create_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(config_ops_frame, text="保存配置", 
                  command=self.save_config).pack(side=tk.LEFT, padx=5)
        
        # 监控控制
        monitor_frame = ttk.LabelFrame(main_frame, text="监控控制")
        monitor_frame.pack(fill=tk.X, pady=5)
        
        control_frame = ttk.Frame(monitor_frame)
        control_frame.pack(pady=10)
        
        self.start_btn = ttk.Button(control_frame, text="开始监控", 
                                   command=self.start_monitoring)
        self.start_btn.pack(side=tk.LEFT, padx=10)
        
        self.stop_btn = ttk.Button(control_frame, text="停止监控", 
                                  command=self.stop_monitoring, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=10)
        
        # 说明文本
        info_frame = ttk.LabelFrame(main_frame, text="使用说明")
        info_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        info_text = tk.Text(info_frame, wrap=tk.WORD, height=8)
        info_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        help_text = """欢迎使用孟子智能助手！

这是简化版本，主要用于测试基本功能。

使用步骤：
1. 创建或选择一个配置文件
2. 点击"开始监控"测试基本功能
3. 如果一切正常，可以尝试运行完整版本

注意事项：
- 这个版本不包含图像识别功能
- 仅用于测试界面和基本逻辑
- 完整功能请运行 XXD.py

热键说明：
- 此版本暂不支持全局热键
- 请使用界面按钮进行操作

如果这个版本运行正常，说明Python环境配置正确。
如果仍有问题，请检查Python版本和tkinter安装。"""
        
        info_text.insert(1.0, help_text)
        info_text.config(state=tk.DISABLED)
        
        # 初始化
        self.refresh_configs()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def refresh_configs(self):
        """刷新配置列表"""
        configs = self.config_manager.get_config_list()
        self.config_combo['values'] = configs
        if configs and not self.config_combo.get():
            self.config_combo.set(configs[0])
    
    def create_config(self):
        """创建配置"""
        config_name = simpledialog.askstring("创建配置", "请输入配置名称:")
        if config_name:
            if self.config_manager.create_config(config_name):
                messagebox.showinfo("成功", f"配置 '{config_name}' 创建成功")
                self.refresh_configs()
                self.config_combo.set(config_name)
            else:
                messagebox.showerror("错误", f"配置 '{config_name}' 创建失败")
    
    def load_config(self):
        """加载配置"""
        selected = self.config_combo.get()
        if not selected:
            messagebox.showwarning("警告", "请选择要加载的配置")
            return
        
        if self.config_manager.load_config(selected):
            messagebox.showinfo("成功", f"配置 '{selected}' 加载成功")
        else:
            messagebox.showerror("错误", f"配置 '{selected}' 加载失败")
    
    def save_config(self):
        """保存配置"""
        if not self.config_manager.current_config_name:
            messagebox.showwarning("警告", "没有当前配置可保存")
            return
        
        messagebox.showinfo("提示", "简化版本暂不支持配置保存")
    
    def start_monitoring(self):
        """开始监控"""
        if not self.config_manager.current_config:
            messagebox.showwarning("警告", "请先加载一个配置")
            return
        
        self.is_monitoring = True
        self.status_var.set("监控中 (模拟)")
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        
        messagebox.showinfo("成功", "监控已启动 (这是模拟模式)")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        self.status_var.set("未监控")
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        
        messagebox.showinfo("成功", "监控已停止")
    
    def on_closing(self):
        """关闭窗口"""
        if messagebox.askokcancel("退出", "确定要退出程序吗？"):
            self.root.destroy()
    
    def run(self):
        """运行界面"""
        self.create_window()
        self.root.mainloop()

def main():
    """主函数"""
    try:
        print("孟子智能助手 v2.0 (简化版) 启动中...")
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            print("错误: 需要Python 3.8或更高版本")
            return
        
        print(f"Python版本: {sys.version}")
        print(f"当前目录: {os.getcwd()}")
        
        # 创建并运行UI
        ui = SimpleUI()
        ui.run()
        
        print("程序已退出")
        
    except Exception as e:
        print(f"程序运行失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
