"""
血量监控模块
实时监控角色血量，在危险时自动触发应对措施
"""

import threading
import time
from typing import Dict, Any, Optional, Callable, List, Tuple
import logging
from .image_recognition import ImageRecognition
from .hotkey_manager import HotkeyManager

class HealthMonitor:
    def __init__(self, image_recognition: ImageRecognition, hotkey_manager: HotkeyManager):
        self.image_recognition = image_recognition
        self.hotkey_manager = hotkey_manager
        self.logger = logging.getLogger(__name__)
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        self.stop_event = threading.Event()
        
        # 配置参数
        self.health_region = [500, 100, 200, 30]  # 默认血条区域
        self.scan_interval = 0.3  # 扫描间隔
        self.priority_mode = "health_first"  # 优先级模式
        
        # 血量阈值和对应动作
        self.health_actions: Dict[int, Dict[str, Any]] = {
            30: {"hotkey": "h", "description": "使用治疗石", "cooldown": 5.0},
            20: {"hotkey": "s", "description": "使用保命技能", "cooldown": 3.0},
            10: {"hotkey": "x", "description": "紧急逃脱", "cooldown": 1.0}
        }
        
        # 冷却时间跟踪
        self.action_cooldowns: Dict[int, float] = {}
        
        # 当前血量状态
        self.current_health = 100.0
        self.health_history: List[Tuple[float, float]] = []  # (timestamp, health)
        self.max_history_length = 100
        
        # 统计信息
        self.stats = {
            'total_scans': 0,
            'health_readings': 0,
            'actions_triggered': 0,
            'lowest_health': 100.0,
            'average_health': 100.0,
            'last_action_time': None,
            'last_action_type': None
        }
        
        # 回调函数
        self.on_health_changed: Optional[Callable] = None
        self.on_health_critical: Optional[Callable] = None
        self.on_action_triggered: Optional[Callable] = None
        
        # 血量检测方法
        self.detection_method = "color_fill"  # color_fill 或 bar_fill
        self.health_color_range = None  # 自定义血条颜色范围
        
        # 智能检测参数
        self.health_trend_window = 5  # 血量趋势分析窗口
        self.emergency_threshold = 15  # 紧急阈值
        self.rapid_decline_threshold = 20  # 快速下降阈值（每秒）
    
    def set_health_region(self, region: list):
        """设置血条区域"""
        self.health_region = region
        self.logger.info(f"血条区域设置为: {region}")
    
    def set_scan_interval(self, interval: float):
        """设置扫描间隔"""
        self.scan_interval = max(0.1, interval)
        self.logger.info(f"血量扫描间隔设置为: {interval}秒")
    
    def set_priority_mode(self, mode: str):
        """
        设置优先级模式
        Args:
            mode: health_first, skill_first, balanced
        """
        if mode in ["health_first", "skill_first", "balanced"]:
            self.priority_mode = mode
            self.logger.info(f"优先级模式设置为: {mode}")
        else:
            self.logger.error(f"无效的优先级模式: {mode}")
    
    def add_health_action(self, threshold: int, hotkey: str, description: str, cooldown: float = 3.0):
        """
        添加血量应对措施
        Args:
            threshold: 血量阈值百分比
            hotkey: 对应热键
            description: 描述
            cooldown: 冷却时间（秒）
        """
        self.health_actions[threshold] = {
            "hotkey": hotkey,
            "description": description,
            "cooldown": cooldown,
            "trigger_count": 0,
            "last_trigger_time": None
        }
        self.logger.info(f"血量应对措施添加: {threshold}% -> {hotkey} ({description})")
    
    def remove_health_action(self, threshold: int):
        """移除血量应对措施"""
        if threshold in self.health_actions:
            del self.health_actions[threshold]
            if threshold in self.action_cooldowns:
                del self.action_cooldowns[threshold]
            self.logger.info(f"血量应对措施移除: {threshold}%")
    
    def get_health_actions(self) -> Dict[int, Dict[str, Any]]:
        """获取所有血量应对措施"""
        return self.health_actions.copy()
    
    def set_detection_method(self, method: str):
        """
        设置血量检测方法
        Args:
            method: color_fill 或 bar_fill
        """
        if method in ["color_fill", "bar_fill"]:
            self.detection_method = method
            self.logger.info(f"血量检测方法设置为: {method}")
        else:
            self.logger.error(f"无效的检测方法: {method}")
    
    def set_health_color_range(self, lower_bound: Tuple[int, int, int], upper_bound: Tuple[int, int, int]):
        """
        设置血条颜色范围
        Args:
            lower_bound: 下界颜色 (B, G, R)
            upper_bound: 上界颜色 (B, G, R)
        """
        self.health_color_range = (lower_bound, upper_bound)
        self.logger.info(f"血条颜色范围设置为: {lower_bound} - {upper_bound}")
    
    def start_monitoring(self):
        """开始血量监控"""
        if self.is_monitoring:
            self.logger.warning("血量监控已在运行中")
            return
        
        self.is_monitoring = True
        self.stop_event.clear()
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info("血量监控已启动")
    
    def stop_monitoring(self):
        """停止血量监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        self.stop_event.set()
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2.0)
        
        self.logger.info("血量监控已停止")
    
    def _monitor_loop(self):
        """血量监控循环"""
        self.logger.info("血量监控循环开始")
        
        while self.is_monitoring and not self.stop_event.is_set():
            try:
                self._scan_health()
                time.sleep(self.scan_interval)
                
            except Exception as e:
                self.logger.error(f"血量监控循环错误: {e}")
                time.sleep(1.0)
        
        self.logger.info("血量监控循环结束")
    
    def _scan_health(self):
        """扫描血量"""
        try:
            # 检测血量
            if self.detection_method == "color_fill":
                health_percentage = self.image_recognition.detect_health_percentage(
                    tuple(self.health_region), self.health_color_range
                )
            else:  # bar_fill
                health_percentage = self.image_recognition.detect_health_bar_fill(
                    tuple(self.health_region)
                )
            
            if health_percentage is None:
                return
            
            self.stats['total_scans'] += 1
            self.stats['health_readings'] += 1
            
            # 更新当前血量
            old_health = self.current_health
            self.current_health = health_percentage
            
            # 记录血量历史
            current_time = time.time()
            self.health_history.append((current_time, health_percentage))
            
            # 限制历史记录长度
            if len(self.health_history) > self.max_history_length:
                self.health_history.pop(0)
            
            # 更新统计信息
            self._update_stats(health_percentage)
            
            # 触发血量变化回调
            if self.on_health_changed and abs(old_health - health_percentage) > 1.0:
                self.on_health_changed(old_health, health_percentage)
            
            # 检查是否需要触发应对措施
            self._check_health_actions(health_percentage)
            
            # 分析血量趋势
            self._analyze_health_trend()
            
        except Exception as e:
            self.logger.error(f"血量扫描失败: {e}")
    
    def _update_stats(self, health_percentage: float):
        """更新统计信息"""
        # 更新最低血量
        if health_percentage < self.stats['lowest_health']:
            self.stats['lowest_health'] = health_percentage
        
        # 计算平均血量
        if len(self.health_history) > 0:
            total_health = sum(health for _, health in self.health_history)
            self.stats['average_health'] = total_health / len(self.health_history)
    
    def _check_health_actions(self, health_percentage: float):
        """检查并触发血量应对措施"""
        current_time = time.time()
        
        # 按阈值从低到高排序，优先处理最危险的情况
        sorted_thresholds = sorted(self.health_actions.keys())
        
        for threshold in sorted_thresholds:
            if health_percentage <= threshold:
                # 检查冷却时间
                if threshold in self.action_cooldowns:
                    if current_time - self.action_cooldowns[threshold] < self.health_actions[threshold]["cooldown"]:
                        continue  # 还在冷却中
                
                # 触发应对措施
                self._trigger_health_action(threshold, health_percentage)
                break  # 只触发一个动作
    
    def _trigger_health_action(self, threshold: int, current_health: float):
        """触发血量应对措施"""
        try:
            action = self.health_actions[threshold]
            hotkey = action["hotkey"]
            description = action["description"]
            
            # 记录冷却时间
            current_time = time.time()
            self.action_cooldowns[threshold] = current_time
            
            # 更新统计信息
            self.stats['actions_triggered'] += 1
            self.stats['last_action_time'] = current_time
            self.stats['last_action_type'] = description
            
            # 更新动作统计
            action['trigger_count'] = action.get('trigger_count', 0) + 1
            action['last_trigger_time'] = current_time
            
            # 触发回调
            if self.on_action_triggered:
                self.on_action_triggered(threshold, hotkey, description, current_health)
            
            # 执行按键
            self.hotkey_manager.press_key_async(hotkey)
            
            self.logger.info(f"血量应对措施触发: {description} (血量: {current_health:.1f}%)")
            
            # 如果血量极低，触发危险回调
            if current_health <= self.emergency_threshold and self.on_health_critical:
                self.on_health_critical(current_health)
            
        except Exception as e:
            self.logger.error(f"触发血量应对措施失败: {e}")
    
    def _analyze_health_trend(self):
        """分析血量趋势"""
        if len(self.health_history) < self.health_trend_window:
            return
        
        try:
            # 获取最近的血量数据
            recent_data = self.health_history[-self.health_trend_window:]
            
            # 计算血量变化率
            time_span = recent_data[-1][0] - recent_data[0][0]
            if time_span <= 0:
                return
            
            health_change = recent_data[-1][1] - recent_data[0][1]
            change_rate = health_change / time_span  # 每秒血量变化
            
            # 检测快速下降
            if change_rate < -self.rapid_decline_threshold:
                self.logger.warning(f"检测到血量快速下降: {change_rate:.1f}%/秒")
                
                # 可以在这里添加预警措施
                if self.on_health_critical:
                    self.on_health_critical(self.current_health)
            
        except Exception as e:
            self.logger.error(f"血量趋势分析失败: {e}")
    
    def get_current_health(self) -> float:
        """获取当前血量百分比"""
        return self.current_health
    
    def get_health_history(self, limit: int = 50) -> List[Tuple[float, float]]:
        """获取血量历史记录"""
        return self.health_history[-limit:] if limit > 0 else self.health_history.copy()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        # 添加动作统计
        stats['action_stats'] = {}
        for threshold, action in self.health_actions.items():
            stats['action_stats'][threshold] = {
                'trigger_count': action.get('trigger_count', 0),
                'last_trigger_time': action.get('last_trigger_time'),
                'description': action['description']
            }
        
        # 添加血量趋势信息
        if len(self.health_history) >= 2:
            recent_trend = self.health_history[-1][1] - self.health_history[-2][1]
            stats['recent_trend'] = recent_trend
        else:
            stats['recent_trend'] = 0
        
        return stats
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_scans': 0,
            'health_readings': 0,
            'actions_triggered': 0,
            'lowest_health': 100.0,
            'average_health': 100.0,
            'last_action_time': None,
            'last_action_type': None
        }
        
        # 重置动作统计
        for action in self.health_actions.values():
            action['trigger_count'] = 0
            action['last_trigger_time'] = None
        
        # 清空历史记录
        self.health_history.clear()
        self.action_cooldowns.clear()
        
        self.logger.info("血量监控统计信息已重置")
    
    def test_health_detection(self) -> Optional[float]:
        """测试血量检测"""
        try:
            if self.detection_method == "color_fill":
                health_percentage = self.image_recognition.detect_health_percentage(
                    tuple(self.health_region), self.health_color_range
                )
            else:
                health_percentage = self.image_recognition.detect_health_bar_fill(
                    tuple(self.health_region)
                )
            
            if health_percentage is not None:
                self.logger.info(f"血量检测测试成功: {health_percentage:.1f}%")
            else:
                self.logger.warning("血量检测测试失败")
            
            return health_percentage
            
        except Exception as e:
            self.logger.error(f"血量检测测试异常: {e}")
            return None
    
    def is_health_critical(self, threshold: int = None) -> bool:
        """检查血量是否危险"""
        if threshold is None:
            threshold = self.emergency_threshold
        return self.current_health <= threshold
    
    def get_priority_level(self) -> int:
        """
        获取当前优先级等级
        Returns:
            1-5的优先级等级，5为最高优先级
        """
        if self.current_health <= 10:
            return 5  # 极危险
        elif self.current_health <= 20:
            return 4  # 很危险
        elif self.current_health <= 30:
            return 3  # 危险
        elif self.current_health <= 50:
            return 2  # 注意
        else:
            return 1  # 安全
