#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化测试版本 - 用于诊断问题
"""

import sys
import os

def test_basic():
    """测试基本功能"""
    print("Python版本:", sys.version)
    print("当前工作目录:", os.getcwd())
    print("脚本路径:", os.path.abspath(__file__))
    
def test_imports():
    """测试导入"""
    print("\n测试标准库导入:")
    
    try:
        import json
        print("✓ json 导入成功")
    except Exception as e:
        print(f"✗ json 导入失败: {e}")
    
    try:
        import threading
        print("✓ threading 导入成功")
    except Exception as e:
        print(f"✗ threading 导入失败: {e}")
    
    try:
        import logging
        print("✓ logging 导入成功")
    except Exception as e:
        print(f"✗ logging 导入失败: {e}")
    
    try:
        from pathlib import Path
        print("✓ pathlib 导入成功")
    except Exception as e:
        print(f"✗ pathlib 导入失败: {e}")

def test_tkinter():
    """测试tkinter"""
    print("\n测试tkinter:")
    try:
        import tkinter as tk
        print("✓ tkinter 导入成功")
        
        # 尝试创建一个简单窗口
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("300x200")
        
        label = tk.Label(root, text="如果你看到这个窗口，说明tkinter工作正常！")
        label.pack(pady=50)
        
        button = tk.Button(root, text="关闭", command=root.destroy)
        button.pack()
        
        print("✓ tkinter 窗口创建成功")
        print("请关闭弹出的窗口继续测试...")
        
        root.mainloop()
        
    except Exception as e:
        print(f"✗ tkinter 测试失败: {e}")

def test_third_party():
    """测试第三方库"""
    print("\n测试第三方库:")
    
    # 测试OpenCV
    try:
        import cv2
        print(f"✓ opencv-python 导入成功, 版本: {cv2.__version__}")
    except Exception as e:
        print(f"✗ opencv-python 导入失败: {e}")
        print("  请运行: pip install opencv-python")
    
    # 测试numpy
    try:
        import numpy as np
        print(f"✓ numpy 导入成功, 版本: {np.__version__}")
    except Exception as e:
        print(f"✗ numpy 导入失败: {e}")
        print("  请运行: pip install numpy")
    
    # 测试PIL
    try:
        from PIL import Image
        print("✓ Pillow 导入成功")
    except Exception as e:
        print(f"✗ Pillow 导入失败: {e}")
        print("  请运行: pip install Pillow")
    
    # 测试keyboard
    try:
        import keyboard
        print("✓ keyboard 导入成功")
    except Exception as e:
        print(f"✗ keyboard 导入失败: {e}")
        print("  请运行: pip install keyboard")
    
    # 测试pyautogui
    try:
        import pyautogui
        print("✓ pyautogui 导入成功")
    except Exception as e:
        print(f"✗ pyautogui 导入失败: {e}")
        print("  请运行: pip install pyautogui")

def test_modules():
    """测试自定义模块"""
    print("\n测试自定义模块:")
    
    # 添加模块路径
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    modules_to_test = [
        'config_manager',
        'image_recognition', 
        'hotkey_manager',
        'skill_monitor',
        'health_monitor',
        'ui_manager'
    ]
    
    for module_name in modules_to_test:
        try:
            module = __import__(f'modules.{module_name}', fromlist=[module_name])
            print(f"✓ modules.{module_name} 导入成功")
        except Exception as e:
            print(f"✗ modules.{module_name} 导入失败: {e}")

def main():
    """主函数"""
    print("孟子智能助手 - 诊断测试")
    print("=" * 50)
    
    test_basic()
    test_imports()
    
    # 询问是否测试tkinter
    try:
        response = input("\n是否测试tkinter界面? (y/n): ").lower().strip()
        if response == 'y':
            test_tkinter()
    except:
        pass
    
    test_third_party()
    test_modules()
    
    print("\n" + "=" * 50)
    print("诊断测试完成")
    print("\n如果看到很多 ✗ 错误，请按照提示安装缺失的包:")
    print("pip install opencv-python numpy Pillow keyboard pyautogui psutil")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
