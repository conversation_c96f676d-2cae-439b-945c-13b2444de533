#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mengzi - World of Warcraft Assistant v2.0 (English Version)
Main program entry - Standard library and tkinter only
"""

import sys
import os
import json
import logging
import threading
import time
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
from pathlib import Path

class SimpleConfigManager:
    """Simplified configuration manager"""
    
    def __init__(self, config_dir="configs"):
        self.config_dir = config_dir
        self.current_config = None
        self.current_config_name = None
        
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
    
    def get_default_config(self):
        """Get default configuration"""
        return {
            "monitor_region": [1193, 755, 50, 50],
            "health_region": [500, 100, 200, 30],
            "settings": {
                "scan_interval": 0.33,
                "threshold": 0.9,
                "key_press_delay": 0.19
            },
            "icon_bindings": {},
            "health_actions": {
                "30": {"hotkey": "h", "description": "Use healing stone"},
                "20": {"hotkey": "s", "description": "Use survival skill"}
            }
        }
    
    def create_config(self, config_name):
        """Create new configuration"""
        try:
            config_path = os.path.join(self.config_dir, f"{config_name}.json")
            if os.path.exists(config_path):
                return False
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.get_default_config(), f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Failed to create config: {e}")
            return False
    
    def load_config(self, config_name):
        """Load configuration"""
        try:
            config_path = os.path.join(self.config_dir, f"{config_name}.json")
            if not os.path.exists(config_path):
                return False
            
            with open(config_path, 'r', encoding='utf-8') as f:
                self.current_config = json.load(f)
            
            self.current_config_name = config_name
            return True
        except Exception as e:
            print(f"Failed to load config: {e}")
            return False
    
    def get_config_list(self):
        """Get configuration list"""
        configs = []
        for file in os.listdir(self.config_dir):
            if file.endswith('.json'):
                configs.append(file[:-5])
        return configs

class SimpleUI:
    """Simplified user interface"""
    
    def __init__(self):
        self.config_manager = SimpleConfigManager()
        self.root = None
        self.is_monitoring = False
        
    def create_window(self):
        """Create main window"""
        self.root = tk.Tk()
        self.root.title("Mengzi - World of Warcraft Assistant v2.0")
        self.root.geometry("700x500")
        
        # Create main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = tk.Label(main_frame, text="Mengzi - WoW Assistant", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Status display
        status_frame = ttk.LabelFrame(main_frame, text="Status")
        status_frame.pack(fill=tk.X, pady=5)
        
        self.status_var = tk.StringVar(value="Not monitoring")
        status_label = tk.Label(status_frame, textvariable=self.status_var, 
                               font=("Arial", 12))
        status_label.pack(pady=5)
        
        # Configuration management
        config_frame = ttk.LabelFrame(main_frame, text="Configuration")
        config_frame.pack(fill=tk.X, pady=5)
        
        # Config selection
        config_select_frame = ttk.Frame(config_frame)
        config_select_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(config_select_frame, text="Config:").pack(side=tk.LEFT)
        
        self.config_combo = ttk.Combobox(config_select_frame, width=20)
        self.config_combo.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(config_select_frame, text="Refresh", 
                  command=self.refresh_configs).pack(side=tk.LEFT, padx=2)
        ttk.Button(config_select_frame, text="Load", 
                  command=self.load_config).pack(side=tk.LEFT, padx=2)
        
        # Config operations
        config_ops_frame = ttk.Frame(config_frame)
        config_ops_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(config_ops_frame, text="Create New", 
                  command=self.create_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(config_ops_frame, text="Save Config", 
                  command=self.save_config).pack(side=tk.LEFT, padx=5)
        
        # Monitor control
        monitor_frame = ttk.LabelFrame(main_frame, text="Monitor Control")
        monitor_frame.pack(fill=tk.X, pady=5)
        
        control_frame = ttk.Frame(monitor_frame)
        control_frame.pack(pady=10)
        
        self.start_btn = ttk.Button(control_frame, text="Start Monitor", 
                                   command=self.start_monitoring)
        self.start_btn.pack(side=tk.LEFT, padx=10)
        
        self.stop_btn = ttk.Button(control_frame, text="Stop Monitor", 
                                  command=self.stop_monitoring, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=10)
        
        # Information text
        info_frame = ttk.LabelFrame(main_frame, text="Instructions")
        info_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        info_text = tk.Text(info_frame, wrap=tk.WORD, height=10)
        info_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        help_text = """Welcome to Mengzi WoW Assistant!

This is the simplified version for testing basic functionality.

Usage Steps:
1. Create or select a configuration file
2. Click "Start Monitor" to test basic functions
3. If everything works, try running the full version

Notes:
- This version does not include image recognition
- Only for testing interface and basic logic
- For full features, run XXD.py

Hotkey Instructions:
- This version does not support global hotkeys
- Please use interface buttons for operations

If this version runs normally, your Python environment is configured correctly.
If there are still issues, please check Python version and tkinter installation.

Full Version Requirements:
- opencv-python (image processing)
- numpy (numerical computing)
- Pillow (image processing)
- keyboard (global hotkeys)
- pyautogui (automation)
- psutil (system monitoring)
- pynput (input listening)

To install dependencies:
pip install opencv-python numpy Pillow keyboard pyautogui psutil pynput

Troubleshooting:
1. If simple version works but full version doesn't = dependency issue
2. If simple version doesn't work = Python environment issue
3. Check logs folder for error details
4. Ensure Python 3.8+ is installed"""
        
        info_text.insert(1.0, help_text)
        info_text.config(state=tk.DISABLED)
        
        # Initialize
        self.refresh_configs()
        
        # Bind close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def refresh_configs(self):
        """Refresh configuration list"""
        configs = self.config_manager.get_config_list()
        self.config_combo['values'] = configs
        if configs and not self.config_combo.get():
            self.config_combo.set(configs[0])
    
    def create_config(self):
        """Create configuration"""
        config_name = simpledialog.askstring("Create Config", "Enter configuration name:")
        if config_name:
            if self.config_manager.create_config(config_name):
                messagebox.showinfo("Success", f"Configuration '{config_name}' created successfully")
                self.refresh_configs()
                self.config_combo.set(config_name)
            else:
                messagebox.showerror("Error", f"Failed to create configuration '{config_name}'")
    
    def load_config(self):
        """Load configuration"""
        selected = self.config_combo.get()
        if not selected:
            messagebox.showwarning("Warning", "Please select a configuration to load")
            return
        
        if self.config_manager.load_config(selected):
            messagebox.showinfo("Success", f"Configuration '{selected}' loaded successfully")
        else:
            messagebox.showerror("Error", f"Failed to load configuration '{selected}'")
    
    def save_config(self):
        """Save configuration"""
        if not self.config_manager.current_config_name:
            messagebox.showwarning("Warning", "No current configuration to save")
            return
        
        messagebox.showinfo("Info", "Configuration saving not supported in simple version")
    
    def start_monitoring(self):
        """Start monitoring"""
        if not self.config_manager.current_config:
            messagebox.showwarning("Warning", "Please load a configuration first")
            return
        
        self.is_monitoring = True
        self.status_var.set("Monitoring (Simulation)")
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        
        messagebox.showinfo("Success", "Monitoring started (This is simulation mode)")
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.is_monitoring = False
        self.status_var.set("Not monitoring")
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        
        messagebox.showinfo("Success", "Monitoring stopped")
    
    def on_closing(self):
        """Close window"""
        if messagebox.askokcancel("Exit", "Are you sure you want to exit?"):
            self.root.destroy()
    
    def run(self):
        """Run interface"""
        self.create_window()
        self.root.mainloop()

def main():
    """Main function"""
    try:
        print("Mengzi WoW Assistant v2.0 (Simplified) starting...")
        
        # Check Python version
        if sys.version_info < (3, 8):
            print("ERROR: Python 3.8 or higher required")
            return
        
        print(f"Python version: {sys.version}")
        print(f"Current directory: {os.getcwd()}")
        
        # Create and run UI
        ui = SimpleUI()
        ui.run()
        
        print("Program exited")
        
    except Exception as e:
        print(f"Program failed: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
