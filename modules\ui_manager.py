"""
UI界面管理模块
基于tkinter的用户界面，提供直观的配置和控制界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import threading
import time
from typing import Dict, Any, Optional, Callable
import logging
import os

class UIManager:
    def __init__(self, config_manager, skill_monitor, health_monitor, hotkey_manager):
        self.config_manager = config_manager
        self.skill_monitor = skill_monitor
        self.health_monitor = health_monitor
        self.hotkey_manager = hotkey_manager
        self.logger = logging.getLogger(__name__)
        
        # 主窗口
        self.root = None
        self.is_running = False
        
        # UI组件
        self.notebook = None
        self.status_frame = None
        self.config_frame = None
        self.skill_frame = None
        self.health_frame = None
        self.stats_frame = None
        
        # 状态变量
        self.monitoring_status = tk.StringVar(value="未监控")
        self.current_config = tk.StringVar(value="无配置")
        self.current_health = tk.StringVar(value="--")
        self.last_skill = tk.StringVar(value="无")
        
        # 实时更新线程
        self.update_thread = None
        self.update_running = False
        
        # 回调函数
        self.on_start_monitoring: Optional[Callable] = None
        self.on_stop_monitoring: Optional[Callable] = None
        self.on_config_changed: Optional[Callable] = None
    
    def create_main_window(self):
        """创建主窗口"""
        self.root = tk.Tk()
        self.root.title("孟子 - 魔兽世界智能助手 v2.0")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 创建菜单栏
        self._create_menu_bar()
        
        # 创建主要界面
        self._create_main_interface()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        self.logger.info("主窗口创建完成")
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="新建配置", command=self.create_new_config)
        file_menu.add_command(label="加载配置", command=self.load_config)
        file_menu.add_command(label="保存配置", command=self.save_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 监控菜单
        monitor_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="监控", menu=monitor_menu)
        monitor_menu.add_command(label="开始监控", command=self.start_monitoring)
        monitor_menu.add_command(label="停止监控", command=self.stop_monitoring)
        monitor_menu.add_separator()
        monitor_menu.add_command(label="设置技能区域", command=self.set_skill_region)
        monitor_menu.add_command(label="设置血条区域", command=self.set_health_region)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def _create_main_interface(self):
        """创建主界面"""
        # 创建状态栏
        self._create_status_bar()
        
        # 创建选项卡
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建各个选项卡
        self._create_config_tab()
        self._create_skill_tab()
        self._create_health_tab()
        self._create_stats_tab()
        
        # 启动实时更新
        self.start_update_thread()
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=5, pady=2)
        
        # 监控状态
        ttk.Label(self.status_frame, text="监控状态:").pack(side=tk.LEFT)
        ttk.Label(self.status_frame, textvariable=self.monitoring_status).pack(side=tk.LEFT, padx=(0, 20))
        
        # 当前配置
        ttk.Label(self.status_frame, text="当前配置:").pack(side=tk.LEFT)
        ttk.Label(self.status_frame, textvariable=self.current_config).pack(side=tk.LEFT, padx=(0, 20))
        
        # 当前血量
        ttk.Label(self.status_frame, text="当前血量:").pack(side=tk.LEFT)
        ttk.Label(self.status_frame, textvariable=self.current_health).pack(side=tk.LEFT, padx=(0, 20))
        
        # 最后技能
        ttk.Label(self.status_frame, text="最后技能:").pack(side=tk.LEFT)
        ttk.Label(self.status_frame, textvariable=self.last_skill).pack(side=tk.LEFT)
    
    def _create_config_tab(self):
        """创建配置选项卡"""
        self.config_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.config_frame, text="基础配置")
        
        # 配置管理区域
        config_group = ttk.LabelFrame(self.config_frame, text="配置管理")
        config_group.pack(fill=tk.X, padx=5, pady=5)
        
        # 配置选择
        ttk.Label(config_group, text="选择配置:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.config_combo = ttk.Combobox(config_group, width=20)
        self.config_combo.grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Button(config_group, text="创建配置", command=self.create_new_config).grid(row=0, column=2, padx=5, pady=2)
        ttk.Button(config_group, text="加载配置", command=self.load_config).grid(row=0, column=3, padx=5, pady=2)
        ttk.Button(config_group, text="保存配置", command=self.save_config).grid(row=0, column=4, padx=5, pady=2)
        
        # 监控设置区域
        monitor_group = ttk.LabelFrame(self.config_frame, text="监控设置")
        monitor_group.pack(fill=tk.X, padx=5, pady=5)
        
        # 扫描间隔
        ttk.Label(monitor_group, text="扫描间隔(秒):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.scan_interval_var = tk.DoubleVar(value=0.33)
        ttk.Scale(monitor_group, from_=0.1, to=2.0, variable=self.scan_interval_var, 
                 orient=tk.HORIZONTAL, length=200).grid(row=0, column=1, padx=5, pady=2)
        ttk.Label(monitor_group, textvariable=self.scan_interval_var).grid(row=0, column=2, padx=5, pady=2)
        
        # 匹配阈值
        ttk.Label(monitor_group, text="匹配阈值:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.threshold_var = tk.DoubleVar(value=0.9)
        ttk.Scale(monitor_group, from_=0.5, to=1.0, variable=self.threshold_var, 
                 orient=tk.HORIZONTAL, length=200).grid(row=1, column=1, padx=5, pady=2)
        ttk.Label(monitor_group, textvariable=self.threshold_var).grid(row=1, column=2, padx=5, pady=2)
        
        # 按键延迟
        ttk.Label(monitor_group, text="按键延迟(秒):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.key_delay_var = tk.DoubleVar(value=0.19)
        ttk.Scale(monitor_group, from_=0.01, to=1.0, variable=self.key_delay_var, 
                 orient=tk.HORIZONTAL, length=200).grid(row=2, column=1, padx=5, pady=2)
        ttk.Label(monitor_group, textvariable=self.key_delay_var).grid(row=2, column=2, padx=5, pady=2)
        
        # 区域设置
        region_group = ttk.LabelFrame(self.config_frame, text="区域设置")
        region_group.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(region_group, text="设置技能区域", command=self.set_skill_region).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(region_group, text="设置血条区域", command=self.set_health_region).pack(side=tk.LEFT, padx=5, pady=5)
        
        # 控制按钮
        control_group = ttk.LabelFrame(self.config_frame, text="监控控制")
        control_group.pack(fill=tk.X, padx=5, pady=5)
        
        self.start_btn = ttk.Button(control_group, text="开始监控", command=self.start_monitoring)
        self.start_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.stop_btn = ttk.Button(control_group, text="停止监控", command=self.stop_monitoring, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5, pady=5)
    
    def _create_skill_tab(self):
        """创建技能选项卡"""
        self.skill_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.skill_frame, text="技能设置")
        
        # 技能列表
        skill_list_group = ttk.LabelFrame(self.skill_frame, text="技能绑定列表")
        skill_list_group.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建技能列表树形控件
        columns = ("技能ID", "热键", "描述", "匹配次数")
        self.skill_tree = ttk.Treeview(skill_list_group, columns=columns, show="headings", height=10)
        
        for col in columns:
            self.skill_tree.heading(col, text=col)
            self.skill_tree.column(col, width=150)
        
        # 滚动条
        skill_scrollbar = ttk.Scrollbar(skill_list_group, orient=tk.VERTICAL, command=self.skill_tree.yview)
        self.skill_tree.configure(yscrollcommand=skill_scrollbar.set)
        
        self.skill_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        skill_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 技能操作按钮
        skill_btn_group = ttk.LabelFrame(self.skill_frame, text="技能操作")
        skill_btn_group.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(skill_btn_group, text="添加技能", command=self.add_skill).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(skill_btn_group, text="删除技能", command=self.remove_skill).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(skill_btn_group, text="测试识别", command=self.test_skill_recognition).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(skill_btn_group, text="刷新列表", command=self.refresh_skill_list).pack(side=tk.LEFT, padx=5, pady=5)
    
    def _create_health_tab(self):
        """创建血量选项卡"""
        self.health_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.health_frame, text="血量设置")
        
        # 血量监控设置
        health_monitor_group = ttk.LabelFrame(self.health_frame, text="血量监控设置")
        health_monitor_group.pack(fill=tk.X, padx=5, pady=5)
        
        # 扫描间隔
        ttk.Label(health_monitor_group, text="血量扫描间隔(秒):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.health_scan_interval_var = tk.DoubleVar(value=0.3)
        ttk.Scale(health_monitor_group, from_=0.1, to=2.0, variable=self.health_scan_interval_var, 
                 orient=tk.HORIZONTAL, length=200).grid(row=0, column=1, padx=5, pady=2)
        ttk.Label(health_monitor_group, textvariable=self.health_scan_interval_var).grid(row=0, column=2, padx=5, pady=2)
        
        # 优先级模式
        ttk.Label(health_monitor_group, text="优先级模式:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.priority_mode_var = tk.StringVar(value="health_first")
        priority_combo = ttk.Combobox(health_monitor_group, textvariable=self.priority_mode_var, 
                                    values=["health_first", "skill_first", "balanced"], state="readonly")
        priority_combo.grid(row=1, column=1, padx=5, pady=2)
        
        # 血量动作列表
        health_action_group = ttk.LabelFrame(self.health_frame, text="血量应对措施")
        health_action_group.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建血量动作列表
        health_columns = ("阈值(%)", "热键", "描述", "冷却时间", "触发次数")
        self.health_tree = ttk.Treeview(health_action_group, columns=health_columns, show="headings", height=8)
        
        for col in health_columns:
            self.health_tree.heading(col, text=col)
            self.health_tree.column(col, width=120)
        
        # 滚动条
        health_scrollbar = ttk.Scrollbar(health_action_group, orient=tk.VERTICAL, command=self.health_tree.yview)
        self.health_tree.configure(yscrollcommand=health_scrollbar.set)
        
        self.health_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        health_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 血量操作按钮
        health_btn_group = ttk.LabelFrame(self.health_frame, text="血量操作")
        health_btn_group.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(health_btn_group, text="添加动作", command=self.add_health_action).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(health_btn_group, text="删除动作", command=self.remove_health_action).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(health_btn_group, text="测试检测", command=self.test_health_detection).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(health_btn_group, text="刷新列表", command=self.refresh_health_list).pack(side=tk.LEFT, padx=5, pady=5)
    
    def _create_stats_tab(self):
        """创建统计选项卡"""
        self.stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.stats_frame, text="统计信息")
        
        # 技能统计
        skill_stats_group = ttk.LabelFrame(self.stats_frame, text="技能监控统计")
        skill_stats_group.pack(fill=tk.X, padx=5, pady=5)
        
        self.skill_stats_text = tk.Text(skill_stats_group, height=8, wrap=tk.WORD)
        skill_stats_scroll = ttk.Scrollbar(skill_stats_group, orient=tk.VERTICAL, command=self.skill_stats_text.yview)
        self.skill_stats_text.configure(yscrollcommand=skill_stats_scroll.set)
        
        self.skill_stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        skill_stats_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 血量统计
        health_stats_group = ttk.LabelFrame(self.stats_frame, text="血量监控统计")
        health_stats_group.pack(fill=tk.X, padx=5, pady=5)
        
        self.health_stats_text = tk.Text(health_stats_group, height=8, wrap=tk.WORD)
        health_stats_scroll = ttk.Scrollbar(health_stats_group, orient=tk.VERTICAL, command=self.health_stats_text.yview)
        self.health_stats_text.configure(yscrollcommand=health_stats_scroll.set)
        
        self.health_stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        health_stats_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 统计操作按钮
        stats_btn_group = ttk.Frame(self.stats_frame)
        stats_btn_group.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(stats_btn_group, text="刷新统计", command=self.refresh_stats).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(stats_btn_group, text="重置统计", command=self.reset_stats).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(stats_btn_group, text="导出统计", command=self.export_stats).pack(side=tk.LEFT, padx=5, pady=5)

    def start_update_thread(self):
        """启动实时更新线程"""
        if not self.update_running:
            self.update_running = True
            self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
            self.update_thread.start()

    def stop_update_thread(self):
        """停止实时更新线程"""
        self.update_running = False

    def _update_loop(self):
        """实时更新循环"""
        while self.update_running:
            try:
                self._update_status()
                time.sleep(1.0)  # 每秒更新一次
            except Exception as e:
                self.logger.error(f"UI更新循环错误: {e}")
                time.sleep(2.0)

    def _update_status(self):
        """更新状态信息"""
        try:
            # 更新监控状态
            if self.skill_monitor.is_monitoring or self.health_monitor.is_monitoring:
                status = "监控中"
                if self.skill_monitor.is_monitoring and self.health_monitor.is_monitoring:
                    status = "双重监控中"
                elif self.skill_monitor.is_monitoring:
                    status = "技能监控中"
                elif self.health_monitor.is_monitoring:
                    status = "血量监控中"
            else:
                status = "未监控"

            self.monitoring_status.set(status)

            # 更新当前配置
            if self.config_manager.current_config_name:
                self.current_config.set(self.config_manager.current_config_name)
            else:
                self.current_config.set("无配置")

            # 更新当前血量
            current_health = self.health_monitor.get_current_health()
            self.current_health.set(f"{current_health:.1f}%")

            # 更新最后技能
            skill_stats = self.skill_monitor.get_stats()
            if skill_stats.get('last_pressed_skill'):
                self.last_skill.set(skill_stats['last_pressed_skill'])
            else:
                self.last_skill.set("无")

        except Exception as e:
            self.logger.error(f"状态更新失败: {e}")

    # 配置管理方法
    def create_new_config(self):
        """创建新配置"""
        config_name = simpledialog.askstring("创建配置", "请输入配置名称:")
        if config_name:
            if self.config_manager.create_config(config_name):
                messagebox.showinfo("成功", f"配置 '{config_name}' 创建成功")
                self.refresh_config_list()
            else:
                messagebox.showerror("错误", f"配置 '{config_name}' 创建失败")

    def load_config(self):
        """加载配置"""
        selected = self.config_combo.get()
        if not selected:
            messagebox.showwarning("警告", "请选择要加载的配置")
            return

        if self.config_manager.load_config(selected):
            messagebox.showinfo("成功", f"配置 '{selected}' 加载成功")
            self._load_config_to_ui()
            if self.on_config_changed:
                self.on_config_changed(selected)
        else:
            messagebox.showerror("错误", f"配置 '{selected}' 加载失败")

    def save_config(self):
        """保存配置"""
        if not self.config_manager.current_config_name:
            messagebox.showwarning("警告", "没有当前配置可保存")
            return

        # 从UI更新配置
        self._save_ui_to_config()

        if self.config_manager.save_config():
            messagebox.showinfo("成功", "配置保存成功")
        else:
            messagebox.showerror("错误", "配置保存失败")

    def refresh_config_list(self):
        """刷新配置列表"""
        configs = self.config_manager.get_config_list()
        self.config_combo['values'] = configs
        if configs and not self.config_combo.get():
            self.config_combo.set(configs[0])

    def _load_config_to_ui(self):
        """从配置加载到UI"""
        if not self.config_manager.current_config:
            return

        # 加载基础设置
        settings = self.config_manager.current_config.get("settings", {})
        self.scan_interval_var.set(settings.get("scan_interval", 0.33))
        self.threshold_var.set(settings.get("threshold", 0.9))
        self.key_delay_var.set(settings.get("key_press_delay", 0.19))
        self.health_scan_interval_var.set(settings.get("health_scan_interval", 0.3))
        self.priority_mode_var.set(settings.get("priority_mode", "health_first"))

        # 刷新技能和血量列表
        self.refresh_skill_list()
        self.refresh_health_list()

    def _save_ui_to_config(self):
        """从UI保存到配置"""
        if not self.config_manager.current_config:
            return

        # 保存基础设置
        self.config_manager.set_setting("scan_interval", self.scan_interval_var.get())
        self.config_manager.set_setting("threshold", self.threshold_var.get())
        self.config_manager.set_setting("key_press_delay", self.key_delay_var.get())
        self.config_manager.set_setting("health_scan_interval", self.health_scan_interval_var.get())
        self.config_manager.set_setting("priority_mode", self.priority_mode_var.get())

    # 监控控制方法
    def start_monitoring(self):
        """开始监控"""
        try:
            # 应用当前设置
            self._apply_settings()

            # 启动监控
            if self.on_start_monitoring:
                self.on_start_monitoring()

            # 更新按钮状态
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)

            messagebox.showinfo("成功", "监控已启动")

        except Exception as e:
            messagebox.showerror("错误", f"启动监控失败: {e}")

    def stop_monitoring(self):
        """停止监控"""
        try:
            if self.on_stop_monitoring:
                self.on_stop_monitoring()

            # 更新按钮状态
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)

            messagebox.showinfo("成功", "监控已停止")

        except Exception as e:
            messagebox.showerror("错误", f"停止监控失败: {e}")

    def _apply_settings(self):
        """应用设置到监控器"""
        self.skill_monitor.set_scan_interval(self.scan_interval_var.get())
        self.skill_monitor.set_threshold(self.threshold_var.get())
        self.skill_monitor.set_key_press_delay(self.key_delay_var.get())

        self.health_monitor.set_scan_interval(self.health_scan_interval_var.get())
        self.health_monitor.set_priority_mode(self.priority_mode_var.get())

    # 区域设置方法
    def set_skill_region(self):
        """设置技能区域"""
        messagebox.showinfo("设置技能区域",
                          "请将鼠标移动到技能提示区域，然后按下回车键确认。\n"
                          "使用方向键可以微调位置。")
        # 这里需要实现区域选择逻辑
        # 由于涉及到屏幕交互，暂时用对话框代替
        self._show_region_input_dialog("技能区域", self.skill_monitor.set_monitor_region)

    def set_health_region(self):
        """设置血条区域"""
        messagebox.showinfo("设置血条区域",
                          "请将鼠标移动到血条区域，然后按下回车键确认。\n"
                          "使用方向键可以微调位置。")
        self._show_region_input_dialog("血条区域", self.health_monitor.set_health_region)

    def _show_region_input_dialog(self, title: str, callback: Callable):
        """显示区域输入对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title(f"设置{title}")
        dialog.geometry("300x200")
        dialog.transient(self.root)
        dialog.grab_set()

        ttk.Label(dialog, text=f"请输入{title}坐标:").pack(pady=10)

        # 坐标输入框
        coord_frame = ttk.Frame(dialog)
        coord_frame.pack(pady=10)

        ttk.Label(coord_frame, text="X:").grid(row=0, column=0, padx=5)
        x_var = tk.IntVar(value=100)
        ttk.Entry(coord_frame, textvariable=x_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(coord_frame, text="Y:").grid(row=0, column=2, padx=5)
        y_var = tk.IntVar(value=100)
        ttk.Entry(coord_frame, textvariable=y_var, width=10).grid(row=0, column=3, padx=5)

        ttk.Label(coord_frame, text="宽度:").grid(row=1, column=0, padx=5)
        w_var = tk.IntVar(value=50)
        ttk.Entry(coord_frame, textvariable=w_var, width=10).grid(row=1, column=1, padx=5)

        ttk.Label(coord_frame, text="高度:").grid(row=1, column=2, padx=5)
        h_var = tk.IntVar(value=50)
        ttk.Entry(coord_frame, textvariable=h_var, width=10).grid(row=1, column=3, padx=5)

        def confirm():
            region = [x_var.get(), y_var.get(), w_var.get(), h_var.get()]
            callback(region)
            dialog.destroy()
            messagebox.showinfo("成功", f"{title}设置成功")

        def cancel():
            dialog.destroy()

        btn_frame = ttk.Frame(dialog)
        btn_frame.pack(pady=20)
        ttk.Button(btn_frame, text="确认", command=confirm).pack(side=tk.LEFT, padx=10)
        ttk.Button(btn_frame, text="取消", command=cancel).pack(side=tk.LEFT, padx=10)

    # 技能管理方法
    def add_skill(self):
        """添加技能"""
        dialog = tk.Toplevel(self.root)
        dialog.title("添加技能")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # 技能信息输入
        ttk.Label(dialog, text="技能ID:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        skill_id_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=skill_id_var, width=20).grid(row=0, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="热键:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        hotkey_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=hotkey_var, width=20).grid(row=1, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="描述:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        desc_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=desc_var, width=20).grid(row=2, column=1, padx=10, pady=5)

        def confirm():
            skill_id = skill_id_var.get().strip()
            hotkey = hotkey_var.get().strip()
            description = desc_var.get().strip()

            if not skill_id or not hotkey:
                messagebox.showerror("错误", "技能ID和热键不能为空")
                return

            # 自动添加技能（从当前监控区域捕获模板）
            if self.skill_monitor.auto_add_skill_from_region(skill_id, hotkey, description):
                messagebox.showinfo("成功", f"技能 '{skill_id}' 添加成功")
                self.refresh_skill_list()
                dialog.destroy()
            else:
                messagebox.showerror("错误", f"技能 '{skill_id}' 添加失败")

        def cancel():
            dialog.destroy()

        btn_frame = ttk.Frame(dialog)
        btn_frame.grid(row=3, column=0, columnspan=2, pady=20)
        ttk.Button(btn_frame, text="确认", command=confirm).pack(side=tk.LEFT, padx=10)
        ttk.Button(btn_frame, text="取消", command=cancel).pack(side=tk.LEFT, padx=10)

    def remove_skill(self):
        """删除技能"""
        selected = self.skill_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要删除的技能")
            return

        item = self.skill_tree.item(selected[0])
        skill_id = item['values'][0]

        if messagebox.askyesno("确认", f"确定要删除技能 '{skill_id}' 吗？"):
            self.skill_monitor.remove_skill_binding(skill_id)
            self.refresh_skill_list()
            messagebox.showinfo("成功", f"技能 '{skill_id}' 已删除")

    def test_skill_recognition(self):
        """测试技能识别"""
        selected = self.skill_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要测试的技能")
            return

        item = self.skill_tree.item(selected[0])
        skill_id = item['values'][0]

        confidence = self.skill_monitor.test_skill_recognition(skill_id)
        if confidence is not None:
            messagebox.showinfo("测试结果", f"技能 '{skill_id}' 识别成功\n置信度: {confidence:.3f}")
        else:
            messagebox.showwarning("测试结果", f"技能 '{skill_id}' 识别失败")

    def refresh_skill_list(self):
        """刷新技能列表"""
        # 清空现有项目
        for item in self.skill_tree.get_children():
            self.skill_tree.delete(item)

        # 添加技能绑定
        skill_bindings = self.skill_monitor.get_skill_bindings()
        for skill_id, binding in skill_bindings.items():
            self.skill_tree.insert("", tk.END, values=(
                skill_id,
                binding.get('hotkey', ''),
                binding.get('description', ''),
                binding.get('match_count', 0)
            ))

    # 血量管理方法
    def add_health_action(self):
        """添加血量应对措施"""
        dialog = tk.Toplevel(self.root)
        dialog.title("添加血量应对措施")
        dialog.geometry("400x250")
        dialog.transient(self.root)
        dialog.grab_set()

        # 输入字段
        ttk.Label(dialog, text="血量阈值(%):").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        threshold_var = tk.IntVar(value=30)
        ttk.Entry(dialog, textvariable=threshold_var, width=20).grid(row=0, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="热键:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        hotkey_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=hotkey_var, width=20).grid(row=1, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="描述:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        desc_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=desc_var, width=20).grid(row=2, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="冷却时间(秒):").grid(row=3, column=0, sticky=tk.W, padx=10, pady=5)
        cooldown_var = tk.DoubleVar(value=3.0)
        ttk.Entry(dialog, textvariable=cooldown_var, width=20).grid(row=3, column=1, padx=10, pady=5)

        def confirm():
            threshold = threshold_var.get()
            hotkey = hotkey_var.get().strip()
            description = desc_var.get().strip()
            cooldown = cooldown_var.get()

            if not hotkey or not description:
                messagebox.showerror("错误", "热键和描述不能为空")
                return

            if threshold <= 0 or threshold > 100:
                messagebox.showerror("错误", "血量阈值必须在1-100之间")
                return

            self.health_monitor.add_health_action(threshold, hotkey, description, cooldown)
            messagebox.showinfo("成功", f"血量应对措施添加成功")
            self.refresh_health_list()
            dialog.destroy()

        def cancel():
            dialog.destroy()

        btn_frame = ttk.Frame(dialog)
        btn_frame.grid(row=4, column=0, columnspan=2, pady=20)
        ttk.Button(btn_frame, text="确认", command=confirm).pack(side=tk.LEFT, padx=10)
        ttk.Button(btn_frame, text="取消", command=cancel).pack(side=tk.LEFT, padx=10)

    def remove_health_action(self):
        """删除血量应对措施"""
        selected = self.health_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要删除的血量应对措施")
            return

        item = self.health_tree.item(selected[0])
        threshold = int(item['values'][0])

        if messagebox.askyesno("确认", f"确定要删除血量阈值 {threshold}% 的应对措施吗？"):
            self.health_monitor.remove_health_action(threshold)
            self.refresh_health_list()
            messagebox.showinfo("成功", f"血量应对措施已删除")

    def test_health_detection(self):
        """测试血量检测"""
        health_percentage = self.health_monitor.test_health_detection()
        if health_percentage is not None:
            messagebox.showinfo("测试结果", f"血量检测成功\n当前血量: {health_percentage:.1f}%")
        else:
            messagebox.showwarning("测试结果", "血量检测失败")

    def refresh_health_list(self):
        """刷新血量列表"""
        # 清空现有项目
        for item in self.health_tree.get_children():
            self.health_tree.delete(item)

        # 添加血量应对措施
        health_actions = self.health_monitor.get_health_actions()
        for threshold, action in health_actions.items():
            self.health_tree.insert("", tk.END, values=(
                threshold,
                action.get('hotkey', ''),
                action.get('description', ''),
                action.get('cooldown', 0),
                action.get('trigger_count', 0)
            ))

    # 统计信息方法
    def refresh_stats(self):
        """刷新统计信息"""
        try:
            # 技能统计
            skill_stats = self.skill_monitor.get_stats()
            skill_text = f"""技能监控统计信息:
总扫描次数: {skill_stats.get('total_scans', 0)}
成功匹配次数: {skill_stats.get('successful_matches', 0)}
按键次数: {skill_stats.get('key_presses', 0)}
成功率: {skill_stats.get('success_rate', 0):.2%}
最后匹配时间: {time.strftime('%H:%M:%S', time.localtime(skill_stats.get('last_match_time', 0))) if skill_stats.get('last_match_time') else '无'}
最后按下技能: {skill_stats.get('last_pressed_skill', '无')}

技能详细统计:
"""

            skill_details = skill_stats.get('skill_stats', {})
            for skill_id, stats in skill_details.items():
                skill_text += f"  {skill_id}: 匹配{stats.get('match_count', 0)}次"
                if stats.get('last_match_time'):
                    skill_text += f" (最后: {time.strftime('%H:%M:%S', time.localtime(stats['last_match_time']))})"
                skill_text += f" - {stats.get('description', '')}\n"

            self.skill_stats_text.delete(1.0, tk.END)
            self.skill_stats_text.insert(1.0, skill_text)

            # 血量统计
            health_stats = self.health_monitor.get_stats()
            health_text = f"""血量监控统计信息:
总扫描次数: {health_stats.get('total_scans', 0)}
血量读取次数: {health_stats.get('health_readings', 0)}
动作触发次数: {health_stats.get('actions_triggered', 0)}
最低血量: {health_stats.get('lowest_health', 100):.1f}%
平均血量: {health_stats.get('average_health', 100):.1f}%
最近趋势: {health_stats.get('recent_trend', 0):+.1f}%
最后动作时间: {time.strftime('%H:%M:%S', time.localtime(health_stats.get('last_action_time', 0))) if health_stats.get('last_action_time') else '无'}
最后动作类型: {health_stats.get('last_action_type', '无')}

血量动作统计:
"""

            action_details = health_stats.get('action_stats', {})
            for threshold, stats in action_details.items():
                health_text += f"  {threshold}%阈值: 触发{stats.get('trigger_count', 0)}次"
                if stats.get('last_trigger_time'):
                    health_text += f" (最后: {time.strftime('%H:%M:%S', time.localtime(stats['last_trigger_time']))})"
                health_text += f" - {stats.get('description', '')}\n"

            self.health_stats_text.delete(1.0, tk.END)
            self.health_stats_text.insert(1.0, health_text)

        except Exception as e:
            self.logger.error(f"刷新统计信息失败: {e}")
            messagebox.showerror("错误", f"刷新统计信息失败: {e}")

    def reset_stats(self):
        """重置统计信息"""
        if messagebox.askyesno("确认", "确定要重置所有统计信息吗？"):
            self.skill_monitor.reset_stats()
            self.health_monitor.reset_stats()
            self.refresh_stats()
            messagebox.showinfo("成功", "统计信息已重置")

    def export_stats(self):
        """导出统计信息"""
        try:
            filename = filedialog.asksaveasfilename(
                title="导出统计信息",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if filename:
                skill_stats = self.skill_monitor.get_stats()
                health_stats = self.health_monitor.get_stats()

                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("孟子 - 魔兽世界智能助手统计报告\n")
                    f.write("=" * 50 + "\n")
                    f.write(f"导出时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                    # 技能统计
                    f.write("技能监控统计:\n")
                    f.write("-" * 30 + "\n")
                    f.write(f"总扫描次数: {skill_stats.get('total_scans', 0)}\n")
                    f.write(f"成功匹配次数: {skill_stats.get('successful_matches', 0)}\n")
                    f.write(f"按键次数: {skill_stats.get('key_presses', 0)}\n")
                    f.write(f"成功率: {skill_stats.get('success_rate', 0):.2%}\n\n")

                    # 血量统计
                    f.write("血量监控统计:\n")
                    f.write("-" * 30 + "\n")
                    f.write(f"总扫描次数: {health_stats.get('total_scans', 0)}\n")
                    f.write(f"血量读取次数: {health_stats.get('health_readings', 0)}\n")
                    f.write(f"动作触发次数: {health_stats.get('actions_triggered', 0)}\n")
                    f.write(f"最低血量: {health_stats.get('lowest_health', 100):.1f}%\n")
                    f.write(f"平均血量: {health_stats.get('average_health', 100):.1f}%\n")

                messagebox.showinfo("成功", f"统计信息已导出到: {filename}")

        except Exception as e:
            self.logger.error(f"导出统计信息失败: {e}")
            messagebox.showerror("错误", f"导出统计信息失败: {e}")

    # 帮助和关于
    def show_help(self):
        """显示帮助信息"""
        help_text = """孟子 - 魔兽世界智能助手使用说明

基本使用流程:
1. 创建或加载配置文件
2. 设置技能监控区域和血条区域
3. 添加技能绑定和血量应对措施
4. 开始监控

热键说明:
- ` (反引号): 开始/停止监控
- F8: 切换血量监控模式
- F9: 开启/关闭自动添加技能
- F10: 快速添加技能
- F11: 设置技能监控区域
- F12: 退出程序
- Ctrl+H: 快速设置血条区域

注意事项:
- 确保游戏窗口可见且未被遮挡
- 建议在稳定的光照条件下设置区域
- 定期保存配置以防丢失设置
- 监控时避免移动游戏窗口

技术支持:
如有问题请查看日志文件或联系开发者
"""

        help_window = tk.Toplevel(self.root)
        help_window.title("使用说明")
        help_window.geometry("600x500")
        help_window.transient(self.root)

        text_widget = tk.Text(help_window, wrap=tk.WORD, padx=10, pady=10)
        scrollbar = ttk.Scrollbar(help_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        text_widget.insert(1.0, help_text)
        text_widget.config(state=tk.DISABLED)

    def show_about(self):
        """显示关于信息"""
        about_text = """孟子 - 魔兽世界智能助手 v2.0

一款专为魔兽世界设计的智能助手程序，通过监控游戏界面中的技能提示区域和血量状态，实现技能自动施放和血量预警功能。

主要功能:
• 技能监控与自动施放
• 血量监控与自动应对
• 多职业配置支持
• 智能优先级系统
• 实时统计分析

技术特点:
• 基于计算机视觉技术
• 无需修改游戏文件
• 不会被检测为外挂
• 支持自定义配置

开发信息:
版本: 2.0.0
开发语言: Python
UI框架: tkinter
图像处理: OpenCV
发布日期: 2025年

免责声明:
本程序仅供学习和研究使用，使用本程序可能违反游戏服务条款，请自行承担风险。开发者不对因使用本程序导致的任何后果负责。
"""

        messagebox.showinfo("关于", about_text)

    def run(self):
        """运行UI"""
        if not self.root:
            self.create_main_window()

        self.is_running = True

        # 初始化界面
        self.refresh_config_list()
        self.refresh_skill_list()
        self.refresh_health_list()
        self.refresh_stats()

        # 启动主循环
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.logger.info("用户中断程序")
        finally:
            self.cleanup()

    def on_closing(self):
        """窗口关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出程序吗？"):
            self.cleanup()
            self.root.destroy()

    def cleanup(self):
        """清理资源"""
        try:
            self.is_running = False
            self.stop_update_thread()

            # 停止监控
            if self.skill_monitor.is_monitoring:
                self.skill_monitor.stop_monitoring()
            if self.health_monitor.is_monitoring:
                self.health_monitor.stop_monitoring()

            # 保存当前配置
            if self.config_manager.current_config_name:
                self._save_ui_to_config()
                self.config_manager.save_config()

            self.logger.info("UI资源清理完成")

        except Exception as e:
            self.logger.error(f"UI清理失败: {e}")
