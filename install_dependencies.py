#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖包安装脚本
"""

import subprocess
import sys
import os

def install_package(package_name):
    """安装单个包"""
    try:
        print(f"正在安装 {package_name}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", package_name], 
                              capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print(f"✓ {package_name} 安装成功")
            return True
        else:
            print(f"✗ {package_name} 安装失败:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"✗ {package_name} 安装异常: {e}")
        return False

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✓ {package_name} 已安装")
        return True
    except ImportError:
        print(f"✗ {package_name} 未安装")
        return False

def main():
    """主函数"""
    print("孟子智能助手 - 依赖包安装")
    print("=" * 40)
    
    # 需要安装的包列表
    packages = [
        ("opencv-python", "cv2"),
        ("numpy", "numpy"),
        ("Pillow", "PIL"),
        ("keyboard", "keyboard"),
        ("pyautogui", "pyautogui"),
        ("psutil", "psutil"),
        ("pynput", "pynput")
    ]
    
    print("检查已安装的包...")
    missing_packages = []
    
    for package_name, import_name in packages:
        if not check_package(package_name, import_name):
            missing_packages.append(package_name)
    
    if not missing_packages:
        print("\n所有依赖包都已安装！")
        return
    
    print(f"\n需要安装 {len(missing_packages)} 个包:")
    for pkg in missing_packages:
        print(f"  - {pkg}")
    
    try:
        response = input("\n是否现在安装这些包? (y/n): ").lower().strip()
        if response != 'y':
            print("安装已取消")
            return
    except:
        print("使用默认选择: 是")
    
    print("\n开始安装依赖包...")
    success_count = 0
    
    for package_name in missing_packages:
        if install_package(package_name):
            success_count += 1
    
    print(f"\n安装完成: {success_count}/{len(missing_packages)} 个包安装成功")
    
    if success_count == len(missing_packages):
        print("✓ 所有依赖包安装成功！")
        print("现在可以运行主程序了: python XXD.py")
    else:
        print("✗ 部分包安装失败，请手动安装失败的包")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
